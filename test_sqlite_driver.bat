@echo off
REM SQLite Driver Test for TauDEM
echo SQLite Driver Test for TauDEM
echo ============================
setlocal EnableDelayedExpansion

REM Set the TauDEM installation directory - modify this if needed
set APP_DIR=C:\Program Files\TauDEM
set CURR_DIR=%CD%

REM Configure proper environment for SQLite support
set GDAL_DATA=%APP_DIR%\share\gdal
set PROJ_LIB=%APP_DIR%\share\proj
set GDAL_DRIVER_PATH=%APP_DIR%\bin
set OGR_DRIVER_PATH=%APP_DIR%\bin;%APP_DIR%\lib\gdalplugins
set PATH=%APP_DIR%\bin;%APP_DIR%\TauDEM5Exe;%PATH%
set GDAL_REGISTRY=SQLITE:sqlite3.dll
set OGR_ENABLED_DRIVERS=ESRI Shapefile,CSV,SQLite,Memory,KML,GeoJSON
set SQLITE_LIST_ALL_TABLES=YES
set CPL_DEBUG=OFF

REM Purpose of echo. commands in batch files:
REM - echo. outputs an empty line to the console
REM - It improves readability by adding spacing between sections
REM - It makes the output more organized and easier to follow
REM - It separates different logical sections of the test

echo.
echo Checking for required DLLs:
echo --------------------------
if exist "%APP_DIR%\bin\sqlite3.dll" (
  echo [FOUND] sqlite3.dll
) else (
  echo [MISSING] sqlite3.dll - This is required for SQLite support!
)

echo.
echo Current OGR_DRIVER_PATH settings:
echo %OGR_DRIVER_PATH%

REM CRITICAL: Change to PROJ directory to ensure proper driver loading
pushd "%APP_DIR%\share\proj"
echo Current directory: %CD%

echo.
echo Checking for SQLite in GDAL formats:
echo ---------------------------------
"%APP_DIR%\bin\ogrinfo.exe" --formats | findstr -i "sqlite"
if %ERRORLEVEL% NEQ 0 (
  echo [WARNING] SQLite driver not found in formats list!
) else (
  echo [SUCCESS] SQLite driver found in formats list
)

echo.
echo Listing all available drivers:
echo --------------------------
"%APP_DIR%\bin\ogrinfo.exe" --formats

echo.
echo Testing SQLite database creation:
echo -----------------------------
set TESTDIR=%TEMP%\sqlite_driver_test
if exist "%TESTDIR%" rmdir /s /q "%TESTDIR%"
mkdir "%TESTDIR%"

echo Creating test data:
echo ID,X,Y > "%TESTDIR%\test.csv"
echo 1,100,200 >> "%TESTDIR%\test.csv"

echo.
echo Attempting to create SQLite database:
"%APP_DIR%\bin\ogr2ogr.exe" -f "SQLite" -dsco SPATIALITE=YES "%TESTDIR%\test.sqlite" "%TESTDIR%\test.csv" -oo X_POSSIBLE_NAMES=X -oo Y_POSSIBLE_NAMES=Y -oo GEOMETRY=AS_XY

if exist "%TESTDIR%\test.sqlite" (
  echo.
  echo [SUCCESS] SQLite database created!
  echo File size: 
  dir "%TESTDIR%\test.sqlite"
  
  echo.
  echo Trying to read SQLite database:
  "%APP_DIR%\bin\ogrinfo.exe" -al "%TESTDIR%\test.sqlite"
  
  echo.
  echo Testing TauDEM with SQLite:
  cd /d "%APP_DIR%\TauDEM5Exe"
  
  REM Create a simple DEM file for testing
  echo Creating a test DEM file...
  "%APP_DIR%\bin\gdal_translate.exe" -of GTiff -a_srs EPSG:4326 -outsize 10 10 -ot Float32 "%TESTDIR%\test.csv" "%TESTDIR%\test_dem.tif"
  
  REM Run a simple TauDEM command using the SQLite database
  echo Running PitRemove on test DEM and storing results in SQLite...
  PitRemove -z "%TESTDIR%\test_dem.tif" -fel "%TESTDIR%\fel.tif" -sqlite "%TESTDIR%\test.sqlite" -table "pitremove_log" > "%TESTDIR%\taudem_output.txt" 2>&1
  
  REM Check if the TauDEM command completed without SQLite errors
  type "%TESTDIR%\taudem_output.txt"
  find "Error Opening OGR Data Source" "%TESTDIR%\taudem_output.txt" > nul
  
  if %ERRORLEVEL% NEQ 0 (
    echo.
    echo [SUCCESS] TauDEM executed without SQLite driver errors
    
    REM Check if the SQLite file was modified (size or timestamp changed)
    for %%F in ("%TESTDIR%\test.sqlite") do set SQLITE_SIZE=%%~zF
    if !SQLITE_SIZE! GTR 0 (
      echo SQLite file exists and has data (!SQLITE_SIZE! bytes)
      echo This confirms TauDEM can use SQLite dependencies correctly
    )
  ) else (
    echo.
    echo [FAILURE] TauDEM encountered errors with SQLite driver
    echo The "Error Opening OGR Data Source" message indicates SQLite dependencies are not correctly configured
  )
) else (
  echo.
  echo [FAILED] Could not create SQLite database
  echo This suggests SQLite driver is not properly configured
)

REM Return to original directory
popd
cd /d "%CURR_DIR%"
echo.
echo Diagnostic Information:
echo ---------------------
echo GDAL version:
"%APP_DIR%\bin\gdal_translate.exe" --version

echo.
echo TauDEM SQLite driver test complete.
pause