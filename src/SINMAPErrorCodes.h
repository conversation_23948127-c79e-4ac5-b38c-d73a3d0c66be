
#ifndef TD_ERROR_CODES

#define TD_NO_ERROR					0
#define TD_FAILED_GRID_OPEN			1
#define TD_FAILED_GRID_SAVE			2
#define TD_FAILED_MEMORY_ALLOC		3
#define TD_STACK_LARGER_THAN_GRID	4
#define TD_FAILED_TO_ENLARGE_STACK	5
#define TD_DROPAN_ENCOUNTERED_ZERO	6
#define TD_COORDINATE_OUTSIDE_GRID	7
#define TD_CHANNEL_NETWORK_MISMATCH	8
#define TD_FAILED_TREEFILE_OPEN		9
#define TD_FAILED_COORDFILE_OPEN	10
#define TD_NEGATIVE_SA_VALUE		11
#define TD_NEGATIVE_OVERLAND_DIST	12
#define TD_INVALID_TABLE_INDEX		13
#define TD_INVALID_TABLE_ROW_INDEX	14
#define TD_TABLE_ROW_MISSING_PARAM	15
#define TD_INVALID_TABLE_COL_INDEX	16
#define TD_PARAM_GRID_NO_DATA		17
#define TD_FAILED_WTR_SHED_GRD_OPEN	18
#define TD_FAILED_CONNECT_GRD_OPEN	19
#define TD_FAILED_CREATE_SHP_FILE	20
#define TD_INVALID_RAINFALL_SHPFILE	21
#define TD_PARAM_COUNT_INCONSISTENT	22
#define TD_INVALID_VARIANT_TYPE		23
#define TD_UNEXPECTED_NODATA_IN_GRD	24
#define TD_FAILED_SAVE_SHPFILE		25
#define TD_FAILED_GRD_INIT			26
#define TD_GRID_SIZE_MISMATCH		27
#define TD_FAILED_SHPFILE_OPEN		28
#define TD_FAILED_GET_HEADER		29
#define TD_INVALID_ARRAY_SIZE		30
#define TD_FAILED_FILE_OPEN			31
#define TD_FLATROUT_NO_CONVERGENCE	32
#define TD_Fortran_Allocate_Error	33
#define TD_Topsetup_Zero_Area_Catchment  34
#define TD_INVALID_SHPFILE			35
#define TD_FIELD_NOT_FOUND			36
#endif //TD_ERROR_CODES