# Default build type
BUILD_TYPE ?= Debug

# Directories
SRC_DIR = src
BUILD_DIR = $(SRC_DIR)/build
CONFIG_FILE = ../config.cmake  # Move up one level to find the config file
INSTALL_DIR = $(shell grep -oP '(?<=CMAKE_INSTALL_PREFIX ")[^"]+' $(CONFIG_FILE))

# Build commands
.PHONY: all debug release clean install uninstall help

all: debug

debug:
	@echo "Building in Debug mode..."
	@mkdir -p $(BUILD_DIR)
	@cd $(BUILD_DIR) && cmake .. -C ../$(CONFIG_FILE) -DCMAKE_BUILD_TYPE=Debug
	@cd $(BUILD_DIR) && make

release:
	@echo "Building in Release mode..."
	@mkdir -p $(BUILD_DIR)
	@cd $(BUILD_DIR) && cmake .. -C ../$(CONFIG_FILE) -DCMAKE_BUILD_TYPE=Release
	@cd $(BUILD_DIR) && make

clean:
	@echo "Cleaning build directory..."
	@rm -rf $(BUILD_DIR)

install:
	@echo "Installing TauDEM..."
	@cd $(BUILD_DIR) && make install

uninstall:
	@echo "Uninstalling TauDEM..."
	@rm -rf $(INSTALL_DIR)

# Help message
.PHONY: help
help:
	@echo "Makefile usage:"
	@echo "  make debug    - Build in Debug mode (default)"
	@echo "  make release  - Build in Release mode"
	@echo "  make clean    - Clean build directory"
	@echo "  make install  - Install TauDEM"
	@echo "  make uninstall - Remove TauDEM installation"
	@echo "  make help     - Show this help message"