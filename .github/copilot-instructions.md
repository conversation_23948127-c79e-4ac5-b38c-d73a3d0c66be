# TauDEM Development Guide

## Project Overview
TauDEM is a parallel C++ suite for terrain analysis using Digital Elevation Models. It's built around MPI-enabled hydrologic algorithms that process large geospatial raster datasets (DEMs) to extract drainage networks, watersheds, and topographic features.

## Core Architecture

### Modular Tool Design
Each TauDEM tool follows a consistent pattern:
- `<toolname>mn.cpp` - Main function with command-line parsing (e.g., `aread8mn.cpp`)
- `<toolname>.cpp/.h` - Core algorithm implementation (e.g., `aread8.cpp`)
- Uses shared infrastructure from `commonLib.cpp/h` and `tiffIO.cpp/h`

### Key Components
- **tdpartition class** (`partition.h`) - Abstract base for spatial data partitioning across MPI processes
- **linearpart template** (`linearpart.h`) - Linear partitioning implementation for parallel processing
- **tiffIO class** (`tiffIO.h`) - GDAL-based raster I/O with support for large files (BIGTIFF)
- **commonLib** - Shared utilities for MPI communication, data structures, and algorithms

### Parallel Processing Pattern
TauDEM uses domain decomposition:
1. Raster data is partitioned across MPI processes using linear strips
2. Border communication handled via `share()`, `passBorders()`, `addBorders()`
3. Global coordination through `ringTerm()` for iterative algorithms

## Development Workflows

### Building
Use platform-specific approaches:
```bash
# macOS - uses Homebrew dependencies
make debug COMPILER=clang TARGET=pitremove

# Linux - uses system packages  
make release COMPILER=linux

# Windows - uses vcpkg
build-windows.bat debug pitremove
```

The build system:
- CMake configuration in `src/CMakeLists.txt` defines tool targets
- `config.cmake` handles platform-specific dependency paths
- Separate debug/release build directories: `src/build-debug/`, `src/build-release/`

### VS Code Integration
Copy platform templates to activate VS Code integration:
```bash
# macOS setup
cp .vscode/tasks-macos.json.template .vscode/tasks.json
cp .vscode/launch-macos.json.template .vscode/launch.json

# Windows setup
copy .vscode\tasks-windows.json.template .vscode\tasks.json
copy .vscode\launch-windows.json.template .vscode\launch.json
```

Key VS Code workflows:
- **Cmd+Shift+P (macOS) or Ctrl+Shift+P (Windows/Linux) → "Tasks: Run Task"** - Access build tasks
- **Cmd+Shift+D (macOS) or Ctrl+Shift+D (Windows/Linux)** - Access debugging panel
- **F5** - Debug with pre-configured launch configs for each tool
- Test data goes in `test_data/input/`, outputs in `test_data/output/`

### Testing Pattern
Tools expect specific file naming in debug configurations:
- Input: `test_data/input/<tool-specific-names>.tif`
- Output: `test_data/output/<tool-specific-names>.tif`
- Check `launch.json` templates for exact file names per tool

## Code Patterns

### C++17 Standard
TauDEM uses C++17 features throughout the codebase:
- Set in `src/CMakeLists.txt`: `set(CMAKE_CXX_STANDARD 17)`
- Filesystem support for path operations
- Template argument deduction and structured bindings where appropriate
- Modern memory management with smart pointers over raw pointers

### Code Formatting
- Follow existing TauDEM conventions for consistency
- Use descriptive variable names (e.g., `flowDirection`, `watershedArea`)
- Maintain consistent indentation (typically 4 spaces or tabs)
- Include header comments with GPL license in new files
- Document public function interfaces and complex algorithms

### Tool Main Function Pattern
```cpp
// Standard structure in *mn.cpp files
int main(int argc, char **argv) {
    // MPI initialization
    // Command line parsing with specific flags per tool
    // Algorithm execution with MPI coordination
    // Cleanup and finalization
}
```

### Algorithm Implementation Pattern
```cpp
// Core algorithms use template partitioning
template<typename T>
int someAlgorithm(char* inputfile, char* outputfile) {
    // Create linearpart<T> for input data
    // Initialize MPI communication patterns
    // Process data partition by partition
    // Handle border exchange between iterations
    // Write results using tiffIO
}
```

### Memory Management
- Use `linearpart<datatype>` templates for automatic MPI-aware memory management
- GDAL handles raster I/O through `tiffIO` wrapper class
- Avoid raw pointers; leverage RAII through existing partition classes

## Platform Considerations

### Dependencies
- **MPI**: Open-MPI (macOS/Linux) or MS-MPI (Windows via vcpkg)
- **GDAL**: Spatial data I/O, version compatibility critical
- **CMake 3.10+**: Build system with C++17 standard

### Cross-Platform Paths
The `config.cmake` file handles platform detection and dependency paths:
- macOS: Homebrew paths (`/opt/homebrew/`)  
- Linux: System package paths (`/usr/`)
- Windows: vcpkg paths (`C:/dev/vcpkg/installed/x64-windows/`)

### Build Artifacts
- Executables: `src/build-{debug,release}/{Debug,Release}/` (Windows) or `src/build-{debug,release}/` (Unix)
- Install target: Platform-specific, typically `/usr/local/taudem` or custom `PREFIX`

## Key Debugging Points

### Common Issues
- **MPI Deadlocks**: Check `ringTerm()` and border communication sequences
- **Memory Access**: Verify `isInPartition()` and `hasAccess()` calls
- **GDAL Errors**: Ensure proper projection and coordinate system handling
- **Large File Support**: BIGTIFF limitations in `tiffIO.h` structs

### Debug Data Flow
1. Set breakpoints in `*mn.cpp` main functions for tool entry points
2. Step into algorithm implementations in corresponding `.cpp` files  
3. Examine partition boundaries and MPI communication in `linearpart` methods
4. Trace raster I/O through `tiffIO` read/write operations

Use `F10` (step over) for MPI calls, `F11` (step into) for algorithm logic.

## Windows Installer

### Inno Setup Script
TauDEM includes a Windows installer built with Inno Setup:
- **Script**: `WindowsInstaller/setup.iss` - Inno Setup installer script
- **Requirements**: Must be compiled on a Windows machine with Inno Setup installed
- **Dependencies**: Automatically installs MS-MPI and Visual C++ redistributables

### Installer Prerequisites
The installer script expects these files in the source directory:
- `msmpisetup.exe` - Microsoft MPI runtime
- `VC_redist.x64.exe` - Visual C++ 2022 redistributable
- `TauDEM_Exe/win_64/` - Directory containing compiled TauDEM executables
- `TauDEMArcGIS/` - Python toolbox files for ArcGIS integration

### Building the Installer
1. Compile TauDEM in release mode: `build-windows.bat release`
2. Open `setup.iss` in Inno Setup on Windows machine
3. Verify source paths and version numbers in script
4. Compile to generate `TauDEM_setup_x64.exe`

The installer handles vcpkg dependency paths and GDAL plugin configuration automatically.
