# Script Name: WatershedGridToShapefile
#
# Created By:  <PERSON>
# Date:        9/29/11

# Import ArcPy site-package and os modules
import arcpy

# Inputs
inlyr = arcpy.GetParameterAsText(0)
desc = arcpy.Describe(inlyr)
w = str(desc.catalogPath)
arcpy.AddMessage("\nInput Elevation file: " + w)

# Output
shfl = arcpy.GetParameterAsText(1)
arcpy.AddMessage("Output Stream Source file: " + shfl)

# Convert tiff to shp
cmd = arcpy.RasterToPolygon_conversion(w, shfl, "NO_SIMPLIFY", "Value")
