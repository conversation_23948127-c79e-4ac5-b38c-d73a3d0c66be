#!/usr/bin/env python3
"""
Build script for RasterUtility and basic testing

This script helps build the RasterUtility shared library and run basic tests
to verify the C++ to Python interface is working correctly.
"""

import os
import sys
import subprocess
import tempfile
import shutil

def run_command(cmd, cwd=None):
    """Run a command and return success status."""
    try:
        print(f"Running: {cmd}")
        result = subprocess.run(cmd, shell=True, cwd=cwd, 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print(f"Error: {result.stderr}")
            return False
        print(f"Success: {result.stdout}")
        return True
    except Exception as e:
        print(f"Failed to run command: {e}")
        return False

def build_library():
    """Build the RasterUtility shared library."""
    print("Building RasterUtility library...")
    
    # Create build directory
    build_dir = "src/build"
    if os.path.exists(build_dir):
        shutil.rmtree(build_dir)
    os.makedirs(build_dir)
    
    # Run cmake
    cmake_cmd = "cmake .."
    if not run_command(cmake_cmd, cwd=build_dir):
        print("CMake configuration failed")
        return False
    
    # Build the libraries (Windows-specific)
    build_cmd = "cmake --build . --target RasterUtility ShapeFileUtility"
    if not run_command(build_cmd, cwd=build_dir):
        print("Build failed, trying alternative build command")
        # Try alternative build commands for Windows
        alt_commands = [
            "msbuild TauDEM.sln /t:RasterUtility;ShapeFileUtility",
            "devenv TauDEM.sln /Build Release /Project RasterUtility",
            "make RasterUtility ShapeFileUtility"  # Fallback for MinGW
        ]
        
        build_success = False
        for cmd in alt_commands:
            if run_command(cmd, cwd=build_dir):
                build_success = True
                break
        
        if not build_success:
            print("All build attempts failed")
            return False
    
    # Copy the Windows DLL files to a location where Python can find them
    lib_files = [
        ("src/build/Debug/RasterUtility.dll", "RasterUtility.dll"),
        ("src/build/Release/RasterUtility.dll", "RasterUtility.dll"),
        ("src/build/RasterUtility.dll", "RasterUtility.dll"),
        ("src/build/Debug/ShapeFileUtility.dll", "ShapeFileUtility.dll"),
        ("src/build/Release/ShapeFileUtility.dll", "ShapeFileUtility.dll"),
        ("src/build/ShapeFileUtility.dll", "ShapeFileUtility.dll"),
    ]
    
    libs_copied = 0
    for lib_file, dest_name in lib_files:
        if os.path.exists(lib_file):
            shutil.copy2(lib_file, dest_name)
            print(f"Copied {lib_file} to {dest_name}")
            libs_copied += 1
    
    if libs_copied > 0:
        return True
    
    print("No library file found after build")
    return False

def create_test_raster():
    """Create a simple test raster using GDAL (for testing purposes)."""
    try:
        from osgeo import gdal, osr
        import numpy as np
        
        # Create a simple 10x10 test raster
        driver = gdal.GetDriverByName('GTiff')
        test_file = 'test_raster.tif'
        
        if os.path.exists(test_file):
            os.remove(test_file)
        
        # Create dataset
        dataset = driver.Create(test_file, 10, 10, 1, gdal.GDT_Int32)
        
        # Set geotransform (left, pixel_width, 0, top, 0, -pixel_height)
        dataset.SetGeoTransform([0, 1, 0, 10, 0, -1])
        
        # Set projection (simple UTM)
        srs = osr.SpatialReference()
        srs.ImportFromEPSG(32633)  # UTM Zone 33N
        dataset.SetProjection(srs.ExportToWkt())
        
        # Create test data - a simple pattern
        test_data = np.arange(1, 101).reshape(10, 10)  # Values 1-100
        
        # Write data
        band = dataset.GetRasterBand(1)
        band.WriteArray(test_data)
        band.SetNoDataValue(-9999)
        
        # Close dataset
        dataset = None
        band = None
        
        print(f"Created test raster: {test_file}")
        return test_file
        
    except ImportError:
        print("GDAL not available for creating test raster")
        return None
    except Exception as e:
        print(f"Failed to create test raster: {e}")
        return None

def test_raster_utility():
    """Test the RasterUtility Python interface."""
    print("Testing RasterUtility...")
    
    # Create test raster
    test_file = create_test_raster()
    if not test_file:
        print("Cannot test without a test raster file")
        return False
    
    try:
        # Add pyfiles to path so we can import utilities
        sys.path.insert(0, 'pyfiles')
        from raster_utility import RasterUtility, ShapeFileUtility
        
        print(f"Testing with file: {test_file}")
        
        # Test RasterUtility functionality
        with RasterUtility(test_file) as raster:
            print(f"Valid raster: {raster.is_valid()}")
            print(f"Has integer data type: {raster.has_integer_data_type()}")
            print(f"Width: {raster.get_width()}")
            print(f"Height: {raster.get_height()}")
            print(f"Pixel width: {raster.get_pixel_width()}")
            print(f"Pixel height: {raster.get_pixel_height()}")
            print(f"No data value: {raster.get_no_data_value()}")
            
            # Test unique values
            unique_vals = raster.get_unique_values(max_values=20)
            print(f"First 20 unique values: {unique_vals}")
        
        # Test ShapeFileUtility (basic test without actual shapefile)
        print("Testing ShapeFileUtility static methods...")
        driver_name = ShapeFileUtility.get_driver_name("test.shp")
        print(f"Driver name for .shp: {driver_name}")
        
        print("Utility tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"RasterUtility test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Clean up test file
        if test_file and os.path.exists(test_file):
            os.remove(test_file)

def test_updated_si_region_tool():
    """Test the updated SIRegionTool with a simple validation."""
    print("Testing updated SIRegionTool validation...")
    
    test_file = create_test_raster()
    if not test_file:
        print("Cannot test SIRegionTool without a test raster")
        return False
    
    try:
        sys.path.insert(0, 'pyfiles')
        
        # Test both versions
        print("Testing updated SIRegionTool...")
        from SIRegionTool_updated import _validate_args as validate_updated
        import Utils
        
        try:
            validate_updated(test_file, None, None, None, 'output.tif', 'output.txt')
            print("Updated validation test passed!")
            result1 = True
        except Utils.ValidationException as e:
            if "does not exist" in str(e):
                print("Updated validation correctly caught missing output directory")
                result1 = True
            else:
                print(f"Unexpected validation error: {e}")
                result1 = False
        except Exception as e:
            print(f"Updated validation test failed: {e}")
            result1 = False
        
        print("Testing no-osgeo SIRegionTool...")
        from SIRegionTool_no_osgeo import _validate_args as validate_no_osgeo
        
        try:
            validate_no_osgeo(test_file, None, None, None, 'output.tif', 'output.txt')
            print("No-osgeo validation test passed!")
            result2 = True
        except Utils.ValidationException as e:
            if "does not exist" in str(e):
                print("No-osgeo validation correctly caught missing output directory")
                result2 = True
            else:
                print(f"Unexpected validation error: {e}")
                result2 = False
        except Exception as e:
            print(f"No-osgeo validation test failed: {e}")
            result2 = False
        
        return result1 and result2
        
    except Exception as e:
        print(f"SIRegionTool test failed: {e}")
        return False
    finally:
        if test_file and os.path.exists(test_file):
            os.remove(test_file)

def main():
    """Main build and test function."""
    print("TauDEM RasterUtility Build and Test Script")
    print("=" * 50)
    
    # Step 1: Build the library
    if not build_library():
        print("Build failed. Exiting.")
        return False
    
    # Step 2: Test RasterUtility
    if not test_raster_utility():
        print("RasterUtility tests failed.")
        return False
    
    # Step 3: Test updated SIRegionTool
    if not test_updated_si_region_tool():
        print("SIRegionTool tests failed.")
        return False
    
    print("\nAll tests passed! RasterUtility is ready to use.")
    print("\nNext steps:")
    print("1. Replace 'from osgeo import gdal' with 'from raster_utility import Open' in your Python files")
    print("2. Replace gdal.Open() calls with Open() calls")
    print("3. Update your code to use the RasterUtility methods instead of GDAL methods")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)