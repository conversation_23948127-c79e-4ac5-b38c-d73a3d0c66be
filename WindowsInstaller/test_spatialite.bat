@echo off
echo TauDEM SpatiaLite Test Utility
echo =============================
echo.

REM Set environment variables
set APP_DIR=%~dp0
set APP_DIR=%APP_DIR:~0,-1%
set GDAL_DATA=%APP_DIR%\share\gdal
set PROJ_LIB=%APP_DIR%\share\proj
set PATH=%APP_DIR%\bin;%PATH%
set GDAL_DRIVER_PATH=%APP_DIR%\bin;%APP_DIR%\lib\gdalplugins
set OGR_DRIVER_PATH=%APP_DIR%\bin;%APP_DIR%\lib\gdalplugins
set SPATIALITE_SECURITY=relaxed
set SPATIALITE_LIBRARY_PATH=%APP_DIR%\bin\mod_spatialite.dll

echo Checking SpatiaLite DLLs:
if exist "%APP_DIR%\bin\spatialite.dll" (
  echo [FOUND] spatialite.dll
) else (
  echo [MISSING] spatialite.dll
)

if exist "%APP_DIR%\bin\mod_spatialite.dll" (
  echo [FOUND] mod_spatialite.dll
) else (
  echo [MISSING] mod_spatialite.dll
)

if exist "%APP_DIR%\bin\sqlite3_mod_spatialite.dll" (
  echo [FOUND] sqlite3_mod_spatialite.dll
) else (
  echo [MISSING] sqlite3_mod_spatialite.dll
)

echo.
echo Testing SpatiaLite functionality:
set TESTDIR=%TEMP%\taudem_spatialite_test
if exist "%TESTDIR%" rmdir /s /q "%TESTDIR%"
mkdir "%TESTDIR%"

echo Creating test point...
echo ID,X,Y > "%TESTDIR%\point.csv"
echo 1,100,200 >> "%TESTDIR%\point.csv"

echo Converting to SpatiaLite...
"%APP_DIR%\bin\ogr2ogr.exe" -f "SQLite" "%TESTDIR%\test_spatialite.sqlite" "%TESTDIR%\point.csv" -oo X_POSSIBLE_NAMES=X -oo Y_POSSIBLE_NAMES=Y -oo GEOMETRY=AS_XY -dsco SPATIALITE=YES

if exist "%TESTDIR%\test_spatialite.sqlite" (
  echo [SUCCESS] SpatiaLite database created!
  
  echo Checking SpatiaLite metadata tables:
  "%APP_DIR%\bin\ogrinfo.exe" "%TESTDIR%\test_spatialite.sqlite" -sql "SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'spatial%%'"
  
  echo.
  echo Running SpatiaLite-specific test:
  "%APP_DIR%\bin\ogrinfo.exe" "%TESTDIR%\test_spatialite.sqlite" -sql "SELECT load_extension('mod_spatialite'); SELECT CheckSpatialMetaData()"
  
  echo.
  echo Trying SpatiaLite spatial function:
  "%APP_DIR%\bin\ogrinfo.exe" "%TESTDIR%\test_spatialite.sqlite" -sql "SELECT load_extension('mod_spatialite'); SELECT ST_AsText(Geometry) FROM point"
) else (
  echo [FAILED] Could not create SpatiaLite database
)

echo.
echo Test complete. Press any key to exit.
pause > nul
