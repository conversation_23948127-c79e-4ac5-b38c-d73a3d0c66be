Starting build process for x64 target in Debug mode...
Cleaning build directory...
Creating build directory...
Changing to build directory...
Running CMake configuration for x64 target (Debug)...
cmake .. -C ../../config.cmake -G "Visual Studio 17 2022" -A x64
loading initial cache file ../../config.cmake
-- Configuring for Windows x64 platform
-- The C compiler identification is MSVC 19.43.34809.0
-- The CXX compiler identification is MSVC 19.43.34809.0
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.43.34808/bin/Hostarm64/x64/cl.exe - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.43.34808/bin/Hostarm64/x64/cl.exe - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found MPI_C: C:/dev/vcpkg/installed/x64-windows/debug/lib/msmpi.lib (found version "2.0")
-- Found MPI_CXX: C:/dev/vcpkg/installed/x64-windows/debug/lib/msmpi.lib (found version "2.0")
-- Found MPI: TRUE (found version "2.0")
-- Found GDAL using CONFIG mode: 3.10.2
-- MPI_FOUND: TRUE
-- MPI_LIBRARIES: C:/dev/vcpkg/installed/x64-windows/debug/lib/msmpi.lib
-- MPI_C_LIBRARIES: C:/dev/vcpkg/installed/x64-windows/debug/lib/msmpi.lib
-- MPI_CXX_LIBRARIES: C:/dev/vcpkg/installed/x64-windows/debug/lib/msmpi.lib
-- Configuring done (4.5s)
-- Generating done (0.3s)
-- Build files have been written to: Y:/src/build
Building project in Debug mode...
cmake --build . --config Debug
MSBuild version 17.13.19+0d9f5a35a for .NET Framework

  1>Checking Build System
  Building Custom Rule Y:/src/CMakeLists.txt
  aread8mn.cpp
  aread8.cpp
C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.43.34808\include\__msvc_ostream.hpp(329,13): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\aread8.vcxproj]
  (compiling source file '../aread8.cpp')
      C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.43.34808\include\__msvc_ostream.hpp(329,13):
      the template instantiation context (the oldest one first) is
          C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.43.34808\include\__msvc_ostream.hpp(678,75):
          see reference to class template instantiation 'std::basic_ostream<char,std::char_traits<char>>' being compiled
          C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.43.34808\include\__msvc_ostream.hpp(322,39):
          while compiling class template member function 'std::basic_ostream<char,std::char_traits<char>> &std::basic_ostream<char,std::char_traits<char>>::operator <<(long)'
              Y:\src\aread8.cpp(275,184):
              see the first reference to 'std::basic_ostream<char,std::char_traits<char>>::operator <<' in 'aread8'
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\aread8.vcxproj]
  (compiling source file '../aread8.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
      Y:\src\linearpart.h(153,18):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
  commonLib.cpp
  tiffIO.cpp
  ReadOutlets.cpp
  Generating Code...
  aread8.vcxproj -> Y:\src\build\Debug\aread8.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  areadinfmn.cpp
  areadinf.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\areadinf.vcxproj]
  (compiling source file '../areadinf.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\areadinf.vcxproj]
  (compiling source file '../areadinf.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  commonLib.cpp
  tiffIO.cpp
  ReadOutlets.cpp
  Generating Code...
  areadinf.vcxproj -> Y:\src\build\Debug\areadinf.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  CatchHydroGeo.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\catchhydrogeo.vcxproj]
  (compiling source file '../CatchHydroGeo.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\catchhydrogeo.vcxproj]
  (compiling source file '../CatchHydroGeo.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  CatchHydroGeomn.cpp
  commonLib.cpp
  tiffIO.cpp
  ReadOutlets.cpp
  Generating Code...
  catchhydrogeo.vcxproj -> Y:\src\build\Debug\catchhydrogeo.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  CatchOutletsmn.cpp
  CatchOutlets.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\catchoutlets.vcxproj]
  (compiling source file '../CatchOutlets.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\catchoutlets.vcxproj]
  (compiling source file '../CatchOutlets.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  commonLib.cpp
  tiffIO.cpp
  ReadOutlets.cpp
  Generating Code...
  catchoutlets.vcxproj -> Y:\src\build\Debug\catchoutlets.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  ConnectDownmn.cpp
  ConnectDown.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\connectdown.vcxproj]
  (compiling source file '../ConnectDown.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\connectdown.vcxproj]
  (compiling source file '../ConnectDown.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  commonLib.cpp
  tiffIO.cpp
  ReadOutlets.cpp
  Generating Code...
  connectdown.vcxproj -> Y:\src\build\Debug\connectdown.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  D8FlowDirmn.cpp
  d8.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\d8flowdir.vcxproj]
  (compiling source file '../d8.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\d8flowdir.vcxproj]
  (compiling source file '../d8.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  Node.cpp
  commonLib.cpp
  tiffIO.cpp
  ReadOutlets.cpp
  Generating Code...
  d8flowdir.vcxproj -> Y:\src\build\Debug\d8flowdir.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  D8flowpathextremeup.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\d8flowpathextremeup.vcxproj]
  (compiling source file '../D8flowpathextremeup.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\d8flowpathextremeup.vcxproj]
  (compiling source file '../D8flowpathextremeup.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  D8FlowPathExtremeUpmn.cpp
  commonLib.cpp
  tiffIO.cpp
  ReadOutlets.cpp
  Generating Code...
  d8flowpathextremeup.vcxproj -> Y:\src\build\Debug\d8flowpathextremeup.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  D8HDistToStrm.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\d8hdisttostrm.vcxproj]
  (compiling source file '../D8HDistToStrm.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\d8hdisttostrm.vcxproj]
  (compiling source file '../D8HDistToStrm.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  D8HDistToStrmmn.cpp
  commonLib.cpp
  tiffIO.cpp
  Generating Code...
  d8hdisttostrm.vcxproj -> Y:\src\build\Debug\d8hdisttostrm.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  DinfAvalanche.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\dinfavalanche.vcxproj]
  (compiling source file '../DinfAvalanche.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\dinfavalanche.vcxproj]
  (compiling source file '../DinfAvalanche.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  DinfAvalanchemn.cpp
  commonLib.cpp
  tiffIO.cpp
  Generating Code...
  dinfavalanche.vcxproj -> Y:\src\build\Debug\dinfavalanche.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  DinfConcLimAccum.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\dinfconclimaccum.vcxproj]
  (compiling source file '../DinfConcLimAccum.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\dinfconclimaccum.vcxproj]
  (compiling source file '../DinfConcLimAccum.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  DinfConcLimAccummn.cpp
  commonLib.cpp
  tiffIO.cpp
  ReadOutlets.cpp
  Generating Code...
  dinfconclimaccum.vcxproj -> Y:\src\build\Debug\dinfconclimaccum.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  dinfdecayaccum.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\dinfdecayaccum.vcxproj]
  (compiling source file '../dinfdecayaccum.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\dinfdecayaccum.vcxproj]
  (compiling source file '../dinfdecayaccum.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  DinfDecayAccummn.cpp
  commonLib.cpp
  tiffIO.cpp
  ReadOutlets.cpp
  Generating Code...
  dinfdecayaccum.vcxproj -> Y:\src\build\Debug\dinfdecayaccum.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  DinfDistDown.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\dinfdistdown.vcxproj]
  (compiling source file '../DinfDistDown.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\dinfdistdown.vcxproj]
  (compiling source file '../DinfDistDown.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  DinfDistDownmn.cpp
  commonLib.cpp
  tiffIO.cpp
  Generating Code...
  dinfdistdown.vcxproj -> Y:\src\build\Debug\dinfdistdown.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  DinfDistUp.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\dinfdistup.vcxproj]
  (compiling source file '../DinfDistUp.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\dinfdistup.vcxproj]
  (compiling source file '../DinfDistUp.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  DinfDistUpmn.cpp
  commonLib.cpp
  tiffIO.cpp
  Generating Code...
  dinfdistup.vcxproj -> Y:\src\build\Debug\dinfdistup.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  DinfFlowDirmn.cpp
  dinf.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\dinfflowdir.vcxproj]
  (compiling source file '../dinf.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\dinfflowdir.vcxproj]
  (compiling source file '../dinf.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  Node.cpp
  commonLib.cpp
  tiffIO.cpp
  ReadOutlets.cpp
  Generating Code...
  dinfflowdir.vcxproj -> Y:\src\build\Debug\dinfflowdir.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  DinfRevAccum.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\dinfrevaccum.vcxproj]
  (compiling source file '../DinfRevAccum.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\dinfrevaccum.vcxproj]
  (compiling source file '../DinfRevAccum.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  DinfRevAccummn.cpp
  commonLib.cpp
  tiffIO.cpp
  Generating Code...
  dinfrevaccum.vcxproj -> Y:\src\build\Debug\dinfrevaccum.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  DinfTransLimAccum.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\dinftranslimaccum.vcxproj]
  (compiling source file '../DinfTransLimAccum.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\dinftranslimaccum.vcxproj]
  (compiling source file '../DinfTransLimAccum.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  DinfTransLimAccummn.cpp
  commonLib.cpp
  tiffIO.cpp
  ReadOutlets.cpp
  Generating Code...
  dinftranslimaccum.vcxproj -> Y:\src\build\Debug\dinftranslimaccum.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  DinfUpDependence.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\dinfupdependence.vcxproj]
  (compiling source file '../DinfUpDependence.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\dinfupdependence.vcxproj]
  (compiling source file '../DinfUpDependence.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  DinfUpDependencemn.cpp
  commonLib.cpp
  tiffIO.cpp
  Generating Code...
  dinfupdependence.vcxproj -> Y:\src\build\Debug\dinfupdependence.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  DropAnalysis.cpp
C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.43.34808\include\__msvc_ostream.hpp(401,13): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\dropanalysis.vcxproj]
  (compiling source file '../DropAnalysis.cpp')
      C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.43.34808\include\__msvc_ostream.hpp(401,13):
      the template instantiation context (the oldest one first) is
          C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.43.34808\include\__msvc_ostream.hpp(678,75):
          see reference to class template instantiation 'std::basic_ostream<char,std::char_traits<char>>' being compiled
          C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.43.34808\include\__msvc_ostream.hpp(394,39):
          while compiling class template member function 'std::basic_ostream<char,std::char_traits<char>> &std::basic_ostream<char,std::char_traits<char>>::operator <<(float)'
              Y:\src\DropAnalysis.cpp(616,55):
              see the first reference to 'std::basic_ostream<char,std::char_traits<char>>::operator <<' in 'dropan'
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\dropanalysis.vcxproj]
  (compiling source file '../DropAnalysis.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
      Y:\src\linearpart.h(153,18):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
  DropAnalysismn.cpp
  commonLib.cpp
  tiffIO.cpp
  ReadOutlets.cpp
  Generating Code...
  dropanalysis.vcxproj -> Y:\src\build\Debug\dropanalysis.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  EditRastermn.cpp
  EditRaster.cpp
Y:\src\EditRaster.cpp(133,20): warning C4477: 'fscanf' : format string '%d' requires an argument of type 'int *', but variadic argument 3 has type 'short *' [Y:\src\build\editraster.vcxproj]
      Y:\src\EditRaster.cpp(133,20):
      consider using '%hd' in the format string
  
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\editraster.vcxproj]
  (compiling source file '../EditRaster.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\editraster.vcxproj]
  (compiling source file '../EditRaster.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  commonLib.cpp
  tiffIO.cpp
  Generating Code...
  editraster.vcxproj -> Y:\src\build\Debug\editraster.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  flowdirconditionmn.cpp
  flowdircond.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\flowdircond.vcxproj]
  (compiling source file '../flowdircond.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\flowdircond.vcxproj]
  (compiling source file '../flowdircond.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  commonLib.cpp
  tiffIO.cpp
  Generating Code...
  flowdircond.vcxproj -> Y:\src\build\Debug\flowdircond.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  gagewatershedmn.cpp
  gagewatershed.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\gagewatershed.vcxproj]
  (compiling source file '../gagewatershed.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\gagewatershed.vcxproj]
  (compiling source file '../gagewatershed.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  commonLib.cpp
  tiffIO.cpp
  ReadOutlets.cpp
  Generating Code...
  gagewatershed.vcxproj -> Y:\src\build\Debug\gagewatershed.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  gridnetmn.cpp
  gridnet.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\gridnet.vcxproj]
  (compiling source file '../gridnet.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\gridnet.vcxproj]
  (compiling source file '../gridnet.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  commonLib.cpp
  tiffIO.cpp
  ReadOutlets.cpp
  Generating Code...
  gridnet.vcxproj -> Y:\src\build\Debug\gridnet.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  InunMapmn.cpp
  InunMap.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\inunmap.vcxproj]
  (compiling source file '../InunMap.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\inunmap.vcxproj]
  (compiling source file '../InunMap.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  commonLib.cpp
  tiffIO.cpp
  Generating Code...
  inunmap.vcxproj -> Y:\src\build\Debug\inunmap.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  LengthArea.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\lengtharea.vcxproj]
  (compiling source file '../LengthArea.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\lengtharea.vcxproj]
  (compiling source file '../LengthArea.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  LengthAreamn.cpp
  commonLib.cpp
  tiffIO.cpp
  Generating Code...
  lengtharea.vcxproj -> Y:\src\build\Debug\lengtharea.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  MoveOutletsToStrm.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\moveoutletstostrm.vcxproj]
  (compiling source file '../MoveOutletsToStrm.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\moveoutletstostrm.vcxproj]
  (compiling source file '../MoveOutletsToStrm.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  MoveOutletsToStrmmn.cpp
  commonLib.cpp
  tiffIO.cpp
  ReadOutlets.cpp
  Generating Code...
  moveoutletstostrm.vcxproj -> Y:\src\build\Debug\moveoutletstostrm.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  PeukerDouglas.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\peukerdouglas.vcxproj]
  (compiling source file '../PeukerDouglas.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\peukerdouglas.vcxproj]
  (compiling source file '../PeukerDouglas.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  PeukerDouglasmn.cpp
  commonLib.cpp
  tiffIO.cpp
  Generating Code...
  peukerdouglas.vcxproj -> Y:\src\build\Debug\peukerdouglas.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  flood.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\pitremove.vcxproj]
  (compiling source file '../flood.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\pitremove.vcxproj]
  (compiling source file '../flood.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  PitRemovemn.cpp
  commonLib.cpp
  tiffIO.cpp
  Generating Code...
  pitremove.vcxproj -> Y:\src\build\Debug\pitremove.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  RetLimFlowmn.cpp
  RetlimFlow.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\retlimflow.vcxproj]
  (compiling source file '../RetlimFlow.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\retlimflow.vcxproj]
  (compiling source file '../RetlimFlow.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  commonLib.cpp
  tiffIO.cpp
  Generating Code...
  retlimflow.vcxproj -> Y:\src\build\Debug\retlimflow.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  SetRegionmn.cpp
  SetRegion.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\setregion.vcxproj]
  (compiling source file '../SetRegion.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\setregion.vcxproj]
  (compiling source file '../SetRegion.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  commonLib.cpp
  tiffIO.cpp
  Generating Code...
  setregion.vcxproj -> Y:\src\build\Debug\setregion.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  SinmapSImn.cpp
  SinmapSI.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\sinmapsi.vcxproj]
  (compiling source file '../SinmapSI.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\sinmapsi.vcxproj]
  (compiling source file '../SinmapSI.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  commonLib.cpp
  tiffIO.cpp
  Generating Code...
  sinmapsi.vcxproj -> Y:\src\build\Debug\sinmapsi.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  SlopeArea.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\slopearea.vcxproj]
  (compiling source file '../SlopeArea.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\slopearea.vcxproj]
  (compiling source file '../SlopeArea.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  SlopeAreamn.cpp
  commonLib.cpp
  tiffIO.cpp
  Generating Code...
  slopearea.vcxproj -> Y:\src\build\Debug\slopearea.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  SlopeAreaRatio.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\slopearearatio.vcxproj]
  (compiling source file '../SlopeAreaRatio.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\slopearearatio.vcxproj]
  (compiling source file '../SlopeAreaRatio.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  SlopeAreaRatiomn.cpp
  commonLib.cpp
  tiffIO.cpp
  Generating Code...
  slopearearatio.vcxproj -> Y:\src\build\Debug\slopearearatio.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  SlopeAveDown.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\slopeavedown.vcxproj]
  (compiling source file '../SlopeAveDown.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\slopeavedown.vcxproj]
  (compiling source file '../SlopeAveDown.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  SlopeAveDownmn.cpp
  commonLib.cpp
  tiffIO.cpp
  Generating Code...
  slopeavedown.vcxproj -> Y:\src\build\Debug\slopeavedown.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  streamnetmn.cpp
  streamnet.cpp
Y:\src\linklib.h(371,15): warning C4804: '>': unsafe use of type 'bool' in operation [Y:\src\build\streamnet.vcxproj]
  (compiling source file '../streamnet.cpp')
  
Y:\src\streamnet.cpp(954,30): warning C4805: '==': unsafe mix of type 'int' and type 'bool' in operation [Y:\src\build\streamnet.vcxproj]
Y:\src\streamnet.cpp(1076,31): warning C4805: '==': unsafe mix of type 'int' and type 'bool' in operation [Y:\src\build\streamnet.vcxproj]
Y:\src\streamnet.cpp(1300,22): warning C4805: '==': unsafe mix of type 'int' and type 'bool' in operation [Y:\src\build\streamnet.vcxproj]
C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.43.34808\include\__msvc_ostream.hpp(292,13): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\streamnet.vcxproj]
  (compiling source file '../streamnet.cpp')
      C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.43.34808\include\__msvc_ostream.hpp(292,13):
      the template instantiation context (the oldest one first) is
          C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.43.34808\include\__msvc_ostream.hpp(678,75):
          see reference to class template instantiation 'std::basic_ostream<char,std::char_traits<char>>' being compiled
          C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.43.34808\include\__msvc_ostream.hpp(277,39):
          while compiling class template member function 'std::basic_ostream<char,std::char_traits<char>> &std::basic_ostream<char,std::char_traits<char>>::operator <<(int)'
              Y:\src\streamnet.cpp(649,55):
              see the first reference to 'std::basic_ostream<char,std::char_traits<char>>::operator <<' in 'netsetup'
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\streamnet.vcxproj]
  (compiling source file '../streamnet.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
      Y:\src\linearpart.h(153,18):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
  commonLib.cpp
  tiffIO.cpp
  ReadOutlets.cpp
  Generating Code...
  streamnet.vcxproj -> Y:\src\build\Debug\streamnet.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  Threshold.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\threshold.vcxproj]
  (compiling source file '../Threshold.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\threshold.vcxproj]
  (compiling source file '../Threshold.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  Thresholdmn.cpp
  commonLib.cpp
  tiffIO.cpp
  Generating Code...
  threshold.vcxproj -> Y:\src\build\Debug\threshold.exe
  Building Custom Rule Y:/src/CMakeLists.txt
  TWImn.cpp
  TWI.cpp
Y:\src\linearpart.h(142,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc [Y:\src\build\twi.vcxproj]
  (compiling source file '../TWI.cpp')
      Y:\src\linearpart.h(142,2):
      the template instantiation context (the oldest one first) is
          Y:\src\createpart.h(57,13):
          see reference to class template instantiation 'linearpart<int16_t>' being compiled
          Y:\src\linearpart.h(125,118):
          while compiling class template member function 'void linearpart<int16_t>::init(long,long,double,double,MPI_Datatype,datatype)'
          with
          [
              datatype=int16_t
          ]
  
Y:\src\linearpart.h(153,18): warning C4477: 'fprintf' : format string '%ld' requires an argument of type 'long', but variadic argument 3 has type 'uint64_t' [Y:\src\build\twi.vcxproj]
  (compiling source file '../TWI.cpp')
      Y:\src\linearpart.h(153,18):
      consider using '%lld' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%Id' in the format string
      Y:\src\linearpart.h(153,18):
      consider using '%I64d' in the format string
  
  commonLib.cpp
  tiffIO.cpp
  Generating Code...
  twi.vcxproj -> Y:\src\build\Debug\twi.exe
  Building Custom Rule Y:/src/CMakeLists.txt
Build completed successfully
Build process finished.
Press any key to continue . . . 
