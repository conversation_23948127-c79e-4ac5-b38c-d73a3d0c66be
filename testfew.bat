Rem Store current dir
set CURRENT_DIR=%CD%

Rem cd C:\Users\<USER>\Desktop\Input
Rem testall > testall.out 2>&1
Rem The 'TDIR' needs to be reset to the directory where the TauDEM executables exist on your machine
Rem Set TDIR=D:\Dropbox\Projects\TauDEM\Taudemdev\Taudem5PCVS2015\x64\Release
set TDIR=C:\Program Files\TauDEM\TauDEM5Exe\Release

Rem Set paths for MPI, TauDEM, and GDAL
set MDIR=C:\Program Files\Microsoft MPI\Bin
set APP_DIR=C:\Program Files\TauDEM

Rem Set essential GDAL environment variables
set GDAL_DATA=%APP_DIR%\share\gdal
set PROJ_LIB=%APP_DIR%\share\proj
set GDAL_DRIVER_PATH=%APP_DIR%\bin
set OGR_DRIVER_PATH=%APP_DIR%\bin
REM set GDAL_REGISTRY=SQLITE:sqlite3.dll
REM set OGR_ENABLED_DRIVERS=ESRI Shapefile,CSV,SqLite,GeoJSON

Rem Include all required directories in the PATH
set PATH=%MDIR%;%TDIR%;%APP_DIR%\bin;%PATH%

REM Enable debugging for detailed error messages
set PROJ_DEBUG=3
rem set CPL_DEBUG=ON 

REM Check for proj.db - this is a critical diagnostic step
if exist "%PROJ_LIB%\proj.db" (
  echo Found proj.db at %PROJ_LIB%\proj.db
) else (
  echo ERROR: proj.db not found at %PROJ_LIB%\proj.db
  echo Searching for proj.db...
  dir /s /b "%APP_DIR%\proj.db"
  pause
  exit /b 1
)
REM Create a temporary dummy file to force PROJ initialization
echo Testing PROJ configuration
if exist "%APP_DIR%\bin\gdal_translate.exe" (
  "%APP_DIR%\bin\gdal_translate.exe" --version > nul 2>&1
)
REM Return to original directory for actual testing
popd
echo Returned to original directory: %CD%

REM Display environment for verification
echo TauDEM Environment:
echo ------------------
echo GDAL_DATA=%GDAL_DATA%
echo PROJ_LIB=%PROJ_LIB%
echo CURRENT_DIR=%CD%
echo.

Rem  Basic grid analysis
cd Base
mpiexec -np 3 PitRemove logan.tif
mpiexec -np 3 PitRemove -z logan.tif -fel loganfel4.tiff -4way
mpiexec -np 3 PitRemove -z logan.tif -fel loganfeldm.tif -depmask logpitmask.tif
mpiexec -np 3 PitRemove -z logan.tif -fel loganfeldm4.tif -depmask logpitmask.tif -4way
mpiexec -n 5 D8Flowdir -p loganp.tif -sd8 logansd8.tif -fel loganfel.tif
mpiexec -n 4 DinfFlowdir -ang loganang.tif -slp loganslp.tif -fel loganfel.tif
mpiexec -n 6 D8Flowdir -p loganpnf.tif -sd8 logansd8nf.tif -fel logan.tif
mpiexec -n 3 DinfFlowdir -ang loganangnf.tif -slp loganslpnf.tif -fel logan.tif
mpiexec -np 4 AreaD8 logan.tif
mpiexec -np 12 AreaDinf logan.tif
mpiexec -np 4 AreaD8 -p loganpnf.tif -ad8 loganad8nf.tif
mpiexec -np 12 AreaDinf -ang loganangnf.tif -sca loganscanf.tif

mpiexec -n 7 aread8 -p loganp.tif -o outlet.shp -ad8 loganad8o.tif
mpiexec -n 1 areadinf -ang loganang.tif -o outlet.shp -sca loganscao.tif
mpiexec -n 5 Gridnet -p loganp.tif -plen loganplen.tif -tlen logantlen.tif -gord logangord.tif 
mpiexec -n 5 Gridnet -p loganp.tif -plen loganplen1.tif -tlen logantlen1.tif -gord logangord1.tif -mask loganad8.tif -thresh 100 
mpiexec -n 7 Gridnet -p loganp.tif -plen loganplen2.tif -tlen logantlen2.tif -gord logangord2.tif -o outlet.shp 

Rem stream network peuker douglas
mpiexec -np 7 PeukerDouglas -fel loganfel.tif -ss loganss.tif
mpiexec -n 4 Aread8 -p loganp.tif -o outlet.shp -ad8 loganssa.tif -wg loganss.tif
mpiexec -n 4 Dropanalysis -p loganp.tif -fel loganfel.tif -ad8 loganad8.tif -ssa loganssa.tif -drp logandrp.txt -o outlet.shp -par 5 500 10 0 
mpiexec -n 5 Threshold -ssa loganssa.tif -src logansrc.tif -thresh 180
mpiexec -n 5 Streamnet -fel loganfel.tif -p loganp.tif -ad8 loganad8.tif -src logansrc.tif -ord loganord3.tif -tree logantree.dat -coord logancoord.dat -net logannet.shp -w loganw.tif -o outlet.shp
cd ..

Rem Testing of OGR starts here 
cd AreaD8_data
mpiexec -n 7 aread8 -p loganp.tif -o Loganoutlet.shp -ad8 loganad8_1.tif

Rem test using sqlite file
mpiexec -n 1 aread8 -p loganp.tif -o LoganSample.sqlite -lyrname LoganOutlet -ad8 loganad8_3.tif

