# TauDEM RasterUtility - GDAL Replacement for Python

This document describes the RasterUtility implementation that replaces Python GDAL dependencies in TauDEM's `SIRegionTool.py` with a C++ utility class built on the existing `tiffIO` infrastructure.

## Overview

The RasterUtility provides a C++ class with Python bindings that can replace `gdal.Open()` calls in Python scripts. It leverages TauDEM's existing `tiffIO` class, which already uses GDAL internally but provides a more controlled interface.

## Files Created

### C++ Implementation
- `src/RasterUtility.h` - Header file with class definition and C interface
- `src/RasterUtility.cpp` - Implementation of the RasterUtility class
- `src/CMakeLists.txt` - Updated to build RasterUtility as a shared library

### Python Integration
- `pyfiles/raster_utility.py` - Python wrapper module for the C++ library
- `pyfiles/SIRegionTool_updated.py` - Updated version of SIRegionTool.py using RasterUtility
- `build_and_test.py` - Build and test script

## Features

### RasterUtility C++ Class
- **File Validation**: Check if raster files exist and are valid
- **Data Type Checking**: Verify if raster contains integer data types
- **Raster Properties**: Get dimensions, pixel size, geotransform, no-data values
- **Data Reading**: Read raster data into memory buffers
- **Geometry Comparison**: Compare raster geometries between files
- **Unique Values**: Extract unique values from raster (useful for parameter regions)

### Python Interface
The Python `raster_utility.py` module provides:
- `RasterUtility` class with context manager support
- `Open()` function that mimics `gdal.Open()`
- Constants that mimic GDAL constants (`GA_ReadOnly`, `GDT_UInt32`, etc.)
- Error handling with custom exceptions

## Building the Library

### Prerequisites
- CMake 3.10 or higher
- C++17 compatible compiler
- MPI development libraries
- GDAL development libraries
- Python 3.x with ctypes support

### Build Steps

1. **Configure and build:**
   ```bash
   cd src
   mkdir build
   cd build
   cmake ..
   make RasterUtility
   ```

2. **Copy library to Python accessible location:**
   ```bash
   # Linux/macOS
   cp libRasterUtility.so ../../
   
   # Windows
   cp RasterUtility.dll ../../
   ```

3. **Test the implementation:**
   ```bash
   cd ../..
   python build_and_test.py
   ```

## Usage Examples

### Basic Raster Validation (replacing gdal.Open())

**Original GDAL code:**
```python
from osgeo import gdal
from gdalconst import GA_ReadOnly

dataSource = gdal.Open(dem_file, GA_ReadOnly)
if not dataSource:
    raise Exception("Cannot open file")
```

**New RasterUtility code:**
```python
from raster_utility import RasterUtility

try:
    with RasterUtility(dem_file) as raster:
        if not raster.is_valid():
            raise Exception("Cannot open file")
except Exception as e:
    raise Exception("Cannot open file")
```

### Checking Data Types

**Original GDAL code:**
```python
band = dataSource.GetRasterBand(1)
if band.DataType not in [gdal.GDT_UInt32, gdal.GDT_UInt16, gdal.GDT_Byte]:
    raise Exception("Not integer data type")
```

**New RasterUtility code:**
```python
if not raster.has_integer_data_type():
    raise Exception("Not integer data type")
```

### Getting Raster Properties

**Original GDAL code:**
```python
geotransform = dataSource.GetGeoTransform()
pixelWidth = geotransform[1]
rows = dataSource.RasterYSize
cols = dataSource.RasterXSize
```

**New RasterUtility code:**
```python
geotransform = raster.get_geotransform()
pixelWidth = raster.get_pixel_width()
rows = raster.get_height()
cols = raster.get_width()
```

### Reading Unique Values

**Original GDAL code:**
```python
# Complex loop to read all data and find unique values
parreg_raster = gdal.Open(parreg)
parreg_band = parreg_raster.GetRasterBand(1)
# ... complex reading logic ...
```

**New RasterUtility code:**
```python
with RasterUtility(parreg) as raster:
    unique_values = raster.get_unique_values()
```

## Migration Guide

### Step 1: Update Imports
Replace:
```python
from osgeo import gdal
from gdalconst import *
```

With:
```python
from raster_utility import RasterUtility, Open, GA_ReadOnly, GDT_UInt32, GDT_UInt16, GDT_Byte
```

### Step 2: Replace gdal.Open() calls
Replace:
```python
dataSource = gdal.Open(filename, GA_ReadOnly)
if not dataSource:
    # error handling
```

With:
```python
try:
    with RasterUtility(filename) as raster:
        if not raster.is_valid():
            # error handling
except Exception:
    # error handling
```

### Step 3: Update Property Access
- `dataSource.RasterXSize` → `raster.get_width()`
- `dataSource.RasterYSize` → `raster.get_height()`
- `dataSource.GetGeoTransform()` → `raster.get_geotransform()`
- `band.GetNoDataValue()` → `raster.get_no_data_value()`

### Step 4: Update Data Type Checking
Replace complex data type checking with:
```python
if raster.has_integer_data_type():
    # handle integer data
```

## Integration with Existing Code

The updated `SIRegionTool_updated.py` demonstrates how to integrate RasterUtility with existing TauDEM Python tools:

1. **Validation Functions**: The `_validate_args()` function now uses RasterUtility for file validation
2. **Property Reading**: Geometric properties are read using RasterUtility methods
3. **Unique Value Extraction**: The `_create_parameter_attribute_table_text_file()` function uses the new `get_unique_values()` method
4. **Hybrid Approach**: Some operations (like shapefile rasterization) still use GDAL where the C++ infrastructure doesn't provide equivalent functionality

## Benefits

1. **Reduced Dependencies**: Python code no longer directly depends on GDAL bindings
2. **Better Performance**: C++ operations are faster than Python GDAL calls
3. **Consistency**: Uses the same raster handling infrastructure as other TauDEM tools
4. **Error Handling**: Better error reporting and handling through the C++ layer
5. **Memory Management**: Automatic cleanup through RAII and Python context managers

## Limitations

1. **Limited GDAL Operations**: Only implements the most commonly used raster operations
2. **Shapefile Support**: Still requires GDAL/OGR for shapefile operations
3. **Projection Handling**: Complex projection operations may still require GDAL
4. **Write Operations**: Currently focused on read operations; write operations use existing utilities

## Troubleshooting

### Library Loading Issues
- Ensure the shared library is in the same directory as your Python script
- Check that all dependencies (MPI, GDAL) are available
- Verify the library was compiled for the correct architecture

### Build Issues
- Ensure GDAL development headers are installed
- Check that MPI is properly configured
- Verify CMake can find the GDAL libraries

### Runtime Errors
- Check file paths are correct and accessible
- Verify input files are valid raster formats supported by GDAL
- Ensure sufficient memory for large raster operations

## Future Enhancements

1. **Extended Operations**: Add support for more raster operations as needed
2. **Write Support**: Implement raster creation and writing capabilities
3. **Memory Optimization**: Optimize memory usage for very large rasters
4. **Error Reporting**: Enhanced error messages and debugging support
5. **Documentation**: Additional examples and use cases

## Testing

The `build_and_test.py` script provides automated testing:
- Builds the shared library
- Creates test raster files
- Tests basic RasterUtility functionality
- Validates the updated SIRegionTool

Run tests with:
```bash
python build_and_test.py
```

## Contributing

When adding new functionality:
1. Update the C++ class with new methods
2. Add corresponding C interface functions
3. Update the Python wrapper
4. Add tests to verify functionality
5. Update documentation

This implementation provides a solid foundation for replacing GDAL dependencies while maintaining compatibility with existing TauDEM infrastructure.