{"version": "2.0.0", "tasks": [{"label": "Build TauDEM (Debug, Clang) - macOS", "type": "shell", "command": "make", "args": ["debug", "COMPILER=clang"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gcc"], "options": {"cwd": "${workspaceFolder}"}, "detail": "Build TauDEM in Debug mode using Clang compiler"}, {"label": "Build pitremove (Debug, Clang) - macOS", "type": "shell", "command": "make", "args": ["debug", "COMPILER=clang", "TARGET=pitremove"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gcc"], "options": {"cwd": "${workspaceFolder}"}, "detail": "Build only pitremove target in Debug mode using Clang compiler"}, {"label": "Build d8flowdir (Debug, Clang) - macOS", "type": "shell", "command": "make", "args": ["debug", "COMPILER=clang", "TARGET=d8flowdir"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gcc"], "options": {"cwd": "${workspaceFolder}"}, "detail": "Build only d8flowdir target in Debug mode using Clang compiler"}, {"label": "Build dinfflowdir (Debug, Clang) - macOS", "type": "shell", "command": "make", "args": ["debug", "COMPILER=clang", "TARGET=dinfflowdir"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gcc"], "options": {"cwd": "${workspaceFolder}"}, "detail": "Build only dinfflowdir target in Debug mode using Clang compiler"}, {"label": "Build aread8 (<PERSON><PERSON><PERSON>, Clang) - macOS", "type": "shell", "command": "make", "args": ["debug", "COMPILER=clang", "TARGET=aread8"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gcc"], "options": {"cwd": "${workspaceFolder}"}, "detail": "Build only aread8 target in Debug mode using Clang compiler"}, {"label": "Build threshold (Debug, Clang) - macOS", "type": "shell", "command": "make", "args": ["debug", "COMPILER=clang", "TARGET=threshold"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gcc"], "options": {"cwd": "${workspaceFolder}"}, "detail": "Build only threshold target in Debug mode using Clang compiler"}, {"label": "Build streamnet (Debug, Clang) - macOS", "type": "shell", "command": "make", "args": ["debug", "COMPILER=clang", "TARGET=streamnet"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gcc"], "options": {"cwd": "${workspaceFolder}"}, "detail": "Build only streamnet target in Debug mode using Clang compiler"}, {"label": "Build gagewatershed (Debug, Clang) - macOS", "type": "shell", "command": "make", "args": ["debug", "COMPILER=clang", "TARGET=gagewatershed"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gcc"], "options": {"cwd": "${workspaceFolder}"}, "detail": "Build only gagewatershed target in Debug mode using Clang compiler"}, {"label": "Build TauDEM (Release, Clang) - macOS", "type": "shell", "command": "make", "args": ["release", "COMPILER=clang"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gcc"], "options": {"cwd": "${workspaceFolder}"}, "detail": "Build TauDEM in Release mode using Clang compiler"}, {"label": "Build dropanalysis (<PERSON><PERSON>g, Clang) - macOS", "type": "shell", "command": "make", "args": ["debug", "COMPILER=clang", "TARGET=dropanalysis"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gcc"], "options": {"cwd": "${workspaceFolder}"}, "detail": "Build only dropanalysis target in Debug mode using Clang compiler"}, {"label": "Build twi (<PERSON>bu<PERSON>, Clang) - macOS", "type": "shell", "command": "make", "args": ["debug", "COMPILER=clang", "TARGET=twi"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gcc"], "options": {"cwd": "${workspaceFolder}"}, "detail": "Build only twi target in Debug mode using Clang compiler"}, {"label": "Build d8hdisttostrm (Debug, Clang) - macOS", "type": "shell", "command": "make", "args": ["debug", "COMPILER=clang", "TARGET=d8hdisttostrm"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gcc"], "options": {"cwd": "${workspaceFolder}"}, "detail": "Build only d8hdisttostrm target in Debug mode using Clang compiler"}, {"label": "Build areadinf (Debug, Clang) - macOS", "type": "shell", "command": "make", "args": ["debug", "COMPILER=clang", "TARGET=areadinf"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gcc"], "options": {"cwd": "${workspaceFolder}"}, "detail": "Build only areadinf target in Debug mode using Clang compiler"}, {"label": "Build Generic Target (Debug, Clang) - macOS", "type": "shell", "command": "make", "args": ["debug", "COMPILER=clang", "TARGET=${input:targetName}"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gcc"], "options": {"cwd": "${workspaceFolder}"}, "detail": "Build a specific target in Debug mode using Clang compiler (prompts for target name)"}, {"label": "Build TauDEM (Debug, GCC-Apple) - macOS", "type": "shell", "command": "make", "args": ["debug", "COMPILER=gcc-apple"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gcc"], "options": {"cwd": "${workspaceFolder}"}, "detail": "Build TauDEM in Debug mode using Apple GCC compiler"}, {"label": "Build TauDEM (Release, GCC-Apple) - macOS", "type": "shell", "command": "make", "args": ["release", "COMPILER=gcc-apple"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gcc"], "options": {"cwd": "${workspaceFolder}"}, "detail": "Build TauDEM in Release mode using Apple GCC compiler"}, {"label": "Build TauDEM (Debug, Homebrew GCC) - macOS", "type": "shell", "command": "make", "args": ["debug", "COMPILER=macos"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gcc"], "options": {"cwd": "${workspaceFolder}"}, "detail": "Build TauDEM in Debug mode using Homebrew GCC compiler"}, {"label": "Build TauDEM (Release, Homebrew GCC) - macOS", "type": "shell", "command": "make", "args": ["release", "COMPILER=macos"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gcc"], "options": {"cwd": "${workspaceFolder}"}, "detail": "Build TauDEM in Release mode using Homebrew GCC compiler"}, {"label": "Clean Build - macOS", "type": "shell", "command": "make", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}"}, "detail": "Clean all build artifacts and binaries"}, {"label": "Install TauDEM - macOS", "type": "shell", "command": "make", "args": ["install", "PREFIX=$HOME/local"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}"}, "detail": "Install TauDEM binaries to $HOME/local/taudem (add $HOME/local/taudem to PATH after install)"}, {"label": "Install TauDEM (Custom Path) - macOS", "type": "shell", "command": "make", "args": ["install", "PREFIX=${input:installPath}"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}"}, "detail": "Install TauDEM binaries to custom location"}, {"label": "Uninstall TauDEM - macOS", "type": "shell", "command": "make", "args": ["uninstall"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}"}, "detail": "Remove TauDEM binaries from system"}, {"label": "Check Dependencies - macOS", "type": "shell", "command": "echo 'Checking TauDEM dependencies on macOS...' && echo '=== GDAL ===' && gdal-config --version && echo '=== MPI ===' && mpirun --version && echo '=== CMake ===' && cmake --version && echo '=== Clang ===' && clang --version | head -1 && echo '=== GCC (if available) ===' && (gcc --version | head -1 || echo 'GCC not found') && echo 'Dependencies check complete.'", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}"}, "detail": "Check if all required dependencies are installed and accessible"}, {"label": "Run Specific TauDEM Tool (debug) - macOS", "type": "shell", "command": "${workspaceFolder}/build/debug/src/${input:taudemTool}", "args": ["${input:taudemArgs}"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}", "env": {"PATH": "${workspaceFolder}/build/debug/src:${env:PATH}"}}, "detail": "Run a specific TauDEM tool from debug build with custom arguments"}, {"label": "Open TauDEM Documentation", "type": "shell", "command": "open", "args": ["http://hydrology.usu.edu/taudem/taudem5/documentation.html"], "group": "test", "presentation": {"echo": true, "reveal": "never", "focus": false, "panel": "shared"}, "detail": "Open TauDEM documentation in default browser"}], "inputs": [{"id": "taudemTool", "description": "TauDEM tool to run", "default": "<PERSON><PERSON>ove", "type": "promptString"}, {"id": "taudemArgs", "description": "Arguments for the TauDEM tool (space-separated)", "default": "-z input.tif -fel output.tif", "type": "promptString"}, {"id": "installPath", "description": "Custom installation path", "default": "/usr/local/taudem", "type": "promptString"}, {"id": "targetName", "description": "Target name to build", "default": "<PERSON><PERSON>ove", "type": "promptString"}]}