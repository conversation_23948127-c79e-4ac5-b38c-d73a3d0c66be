@echo off
echo SQLite Test for TauDEM and GDAL/OGR
echo ===================================

REM Setup environment variables similar to testfew.bat
set TDIR=C:\Program Files\TauDEM\TauDEM5Exe\Release
set MDIR=C:\Program Files\Microsoft MPI\Bin
set APP_DIR=C:\Program Files\TauDEM

REM Set GDAL environment variables
set GDAL_DATA=%APP_DIR%\share\gdal
set PROJ_LIB=%APP_DIR%\share\proj
set PATH=%MDIR%;%TDIR%;%APP_DIR%\bin;%PATH%

REM Enable detailed logging
set CPL_DEBUG=ON
set OGR_ENABLED_DRIVERS=ESRI Shapefile,SQLite,GeoJSON
set GDAL_DRIVER_PATH=%APP_DIR%\bin
set OGR_DRIVER_PATH=%APP_DIR%\bin

echo.
echo Current directory: %CD%
echo.

REM Test write permissions in current directory
echo Testing write permissions...
echo test > test_write_permission.txt
if %ERRORLEVEL% NEQ 0 (
  echo WARNING: Cannot write to current directory - this may explain SQLite failures
) else (
  echo Successfully wrote to current directory
  del test_write_permission.txt
)

REM Use temp directory as a fallback
set TEMP_DIR=%TEMP%
echo Will also test using Windows TEMP directory: %TEMP_DIR%

REM Test GDAL/OGR installation and SQLite support
echo Testing GDAL/OGR installation...
gdalinfo --version
ogrinfo --formats | findstr SQLite

echo.
echo Testing SQLite driver directly...
echo 1. Trying to create a simple SQLite file in current directory
ogrinfo -so -al "SQLite:test_ogr.sqlite"
if %ERRORLEVEL% NEQ 0 (
  echo Failed to create/access SQLite file with OGR in current directory
  
  echo.
  echo 1b. Trying in TEMP directory instead...
  ogrinfo -so -al "SQLite:%TEMP_DIR%\test_ogr.sqlite"
  if %ERRORLEVEL% NEQ 0 (
    echo Failed to create SQLite even in TEMP directory
    echo This suggests a GDAL/SQLite configuration issue rather than permissions
  ) else (
    echo Successfully created SQLite file in TEMP directory
    echo Original error was likely due to permissions in the current directory
  )
) else (
  echo Successfully created/accessed SQLite file with OGR
)

echo.
echo 2. Testing TauDEM with SQLite...
cd AreaD8_data
echo Current working directory: %CD%

REM Clean up any previous test files
if exist "LoganTest.sqlite" del LoganTest.sqlite

REM Run the SQLite test with verbose output
echo Running TauDEM AreaD8 with SQLite output...
mpiexec -n 1 aread8 -p loganp.tif -o LoganTest.sqlite -lyrname LoganOutlets -ad8 loganad8_test.tif -v
echo Exit code: %ERRORLEVEL%

REM If failed, try in TEMP directory
if %ERRORLEVEL% NEQ 0 (
  echo.
  echo 2b. Trying TauDEM with SQLite in TEMP directory...
  mpiexec -n 1 aread8 -p loganp.tif -o "%TEMP_DIR%\LoganTest.sqlite" -lyrname LoganOutlets -ad8 loganad8_test.tif -v
  echo Exit code: %ERRORLEVEL%
)

REM Check if either SQLite file was created
if exist "LoganTest.sqlite" (
  echo SQLite file was created successfully in current directory
  echo Checking content with ogrinfo:
  ogrinfo -so -al LoganTest.sqlite
) else if exist "%TEMP_DIR%\LoganTest.sqlite" (
  echo SQLite file was created successfully in TEMP directory
  echo Checking content with ogrinfo:
  ogrinfo -so -al "%TEMP_DIR%\LoganTest.sqlite"
) else (
  echo ERROR: SQLite file was not created in either location
)

echo.
echo Additional SQLite diagnostic information:
echo SQLite version used by GDAL:
ogrinfo --version

echo.
echo SQLite test complete
echo.
echo SUMMARY:
echo If tests failed in current directory but succeeded in TEMP,
echo the issue is likely permissions-related.
echo Try running TauDEM commands with output to %TEMP_DIR%
echo or running the command prompt as Administrator.
