set(MPI_C_COMPILER "/opt/homebrew/bin/mpicc" CACHE PATH "MPI C compiler")
set(MPI_CXX_COMPILER "/opt/homebrew/bin/mpic++" CACHE PATH "MPI C++ compiler")
set(MPI_C_LIBRARIES "/opt/homebrew/lib/libmpi.dylib" CACHE PATH "MPI libraries")
set(MPI_C_INCLUDE_PATH "/opt/homebrew/include" CACHE PATH "MPI include path")
set(CMAKE_PREFIX_PATH "/opt/homebrew/opt/gdal" CACHE PATH "GDAL installation path")
set(CMAKE_INSTALL_PREFIX "/Users/<USER>/Workspace/SoftwareProjects/TauDEM/install" CACHE PATH "Installation directory")
set(CMAKE_BUILD_TYPE "Debug" CACHE STRING "Build type (Debug/Release)")