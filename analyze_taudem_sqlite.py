import sqlite3
import os
import sys
import tempfile
import subprocess
from pathlib import Path

def analyze_sqlite_database(db_path):
    """Analyze a SQLite database to determine its structure."""
    if not os.path.exists(db_path):
        print(f"Database file {db_path} does not exist")
        return
    
    print(f"Analyzing SQLite database: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get list of all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"Found {len(tables)} tables:")
        
        for table in tables:
            table_name = table[0]
            print(f"\nTable: {table_name}")
            
            # Get schema for this table
            cursor.execute(f"PRAGMA table_info({table_name});")
            columns = cursor.fetchall()
            print("Columns:")
            for col in columns:
                print(f"  {col[1]} ({col[2]})")
            
            # Get row count
            cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
            row_count = cursor.fetchone()[0]
            print(f"Row count: {row_count}")
            
            # Show sample data if available
            if row_count > 0:
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 5;")
                sample_data = cursor.fetchall()
                print("Sample data:")
                for row in sample_data:
                    print(f"  {row}")
        
        conn.close()
    except sqlite3.Error as e:
        print(f"SQLite error: {e}")

def run_taudem_test(temp_dir=None):
    """Run a similar TauDEM test and analyze the resulting database."""
    if temp_dir is None:
        temp_dir = tempfile.mkdtemp(prefix="taudem_sqlite_analysis_")
    else:
        temp_dir = Path(temp_dir)
        os.makedirs(temp_dir, exist_ok=True)
    
    print(f"Using temp directory: {temp_dir}")
    
    # Create a simple test DEM file
    dem_file = os.path.join(temp_dir, "test_dem.tif")
    db_file = os.path.join(temp_dir, "test_output.sqlite")
    
    # You may need to adjust this command based on your environment
    # This is a placeholder - actual DEM creation would depend on your setup
    # subprocess.run(["gdal_create", dem_file, "-size", "100", "100", "-bands", "1"])
    
    # Run PitRemove (adjust path as needed)
    # subprocess.run(["PitRemove", "-z", dem_file, "-fel", db_file])
    
    # For now, analyze the existing database from the test
    test_db = r"C:\Users\<USER>\AppData\Local\Temp\sqlite_driver_test\test.sqlite"
    if os.path.exists(test_db):
        analyze_sqlite_database(test_db)
    else:
        print(f"Test database not found at {test_db}")
        print("Please provide the path to the database:")
        db_path = input("> ")
        if db_path and os.path.exists(db_path):
            analyze_sqlite_database(db_path)

if __name__ == "__main__":
    if len(sys.argv) > 1:
        analyze_sqlite_database(sys.argv[1])
    else:
        run_taudem_test()
