@echo off
echo SQLite Driver Fix Utility for TauDEM
echo =====================================
echo.

REM Set environment variables
set APP_DIR=C:\Program Files\TauDEM
set GDAL_DATA=%APP_DIR%\share\gdal
set PROJ_LIB=%APP_DIR%\share\proj
set PATH=%APP_DIR%\bin;%APP_DIR%\TauDEM5Exe;%PATH%
set GDAL_DRIVER_PATH=%APP_DIR%\bin;%APP_DIR%\lib\gdalplugins
set OGR_DRIVER_PATH=%APP_DIR%\bin;%APP_DIR%\lib\gdalplugins

REM Critical for SQLite drivers
set GDAL_REGISTRY=
set OGR_ENABLED_DRIVERS=
set CPL_DEBUG=ON
set CPL_LOG=%TEMP%\gdal_debug.log

echo Current Configuration:
echo ---------------------
echo GDAL_DATA=%GDAL_DATA%
echo PROJ_LIB=%PROJ_LIB%
echo GDAL_DRIVER_PATH=%GDAL_DRIVER_PATH%
echo OGR_DRIVER_PATH=%OGR_DRIVER_PATH%
echo.

echo Step 1: Verifying available drivers
echo ----------------------------------
echo Looking for SQLite driver:
"%APP_DIR%\bin\ogrinfo.exe" --formats | findstr -i "sqlite"

echo.
echo Step 2: Creating test SQLite file
echo --------------------------------
set TESTDIR=%TEMP%\taudem_sqlite_test
if exist "%TESTDIR%" rmdir /s /q "%TESTDIR%"
mkdir "%TESTDIR%"

REM Create a CSV file with point coordinates
echo ID,X,Y > "%TESTDIR%\point.csv"
echo 1,432152,4662489 >> "%TESTDIR%\point.csv"

REM Test creating SQLite file
echo Testing creation of SQLite file:
"%APP_DIR%\bin\ogr2ogr.exe" -f "SQLite" "%TESTDIR%\test.sqlite" "%TESTDIR%\point.csv" -oo X_POSSIBLE_NAMES=X -oo Y_POSSIBLE_NAMES=Y -oo GEOMETRY=AS_XY -dsco SPATIALITE=YES -lco LAUNDER=NO

if exist "%TESTDIR%\test.sqlite" (
    echo SUCCESS: SQLite file created at %TESTDIR%\test.sqlite
    dir "%TESTDIR%\test.sqlite"
    
    echo.
    echo Step 3: Testing SQLite file read capability
    echo -----------------------------------------
    "%APP_DIR%\bin\ogrinfo.exe" -al "%TESTDIR%\test.sqlite"
    
    echo.
    echo Step 4: Testing with TauDEM (requires sample files)
    echo -------------------------------------------------
    if exist "loganp.tif" (
        echo Found loganp.tif, running TauDEM test...
        mpiexec -n 1 aread8 -p loganp.tif -o "%TESTDIR%\test.sqlite" -lyrname outlet -ad8 "%TESTDIR%\loganad8_test.tif"
        if exist "%TESTDIR%\loganad8_test.tif" (
            echo SUCCESS: TauDEM processed the SQLite file!
        ) else (
            echo FAILED: TauDEM could not process the SQLite file.
        )
    ) else (
        echo Sample file loganp.tif not found, skipping TauDEM test.
    )
) else (
    echo FAILED: Could not create SQLite file
)

echo.
echo Step 5: Installing SQLite driver fixes
echo -----------------------------------
echo Applying system-wide fixes (requires admin rights)...

echo @echo off > "%TEMP%\sqlite_fix_admin.bat"
echo echo Installing SQLite driver fixes for GDAL/OGR... >> "%TEMP%\sqlite_fix_admin.bat"
echo. >> "%TEMP%\sqlite_fix_admin.bat"
echo REM Reset any restrictive driver settings >> "%TEMP%\sqlite_fix_admin.bat"
echo reg delete "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v GDAL_REGISTRY /f ^>nul 2^>^&1 >> "%TEMP%\sqlite_fix_admin.bat"
echo reg delete "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v OGR_ENABLED_DRIVERS /f ^>nul 2^>^&1 >> "%TEMP%\sqlite_fix_admin.bat"
echo. >> "%TEMP%\sqlite_fix_admin.bat"
echo REM Set correct paths for drivers >> "%TEMP%\sqlite_fix_admin.bat"
echo setx GDAL_DRIVER_PATH "%APP_DIR%\bin;%APP_DIR%\lib\gdalplugins" /M >> "%TEMP%\sqlite_fix_admin.bat"
echo setx OGR_DRIVER_PATH "%APP_DIR%\bin;%APP_DIR%\lib\gdalplugins" /M >> "%TEMP%\sqlite_fix_admin.bat"
echo. >> "%TEMP%\sqlite_fix_admin.bat"
echo REM Install SQLite-specific settings >> "%TEMP%\sqlite_fix_admin.bat"
echo setx GDAL_SQLITE_PRAGMA "EMPTY_RESULT_CALLBACKS=ON" /M >> "%TEMP%\sqlite_fix_admin.bat"
echo. >> "%TEMP%\sqlite_fix_admin.bat"
echo echo Fixes applied. Please reboot your system for changes to take effect. >> "%TEMP%\sqlite_fix_admin.bat"
echo pause >> "%TEMP%\sqlite_fix_admin.bat"

powershell -Command "Start-Process cmd -ArgumentList '/c %TEMP%\sqlite_fix_admin.bat' -Verb RunAs"

echo.
echo Step 6: Creating test script for your specific command
echo ---------------------------------------------------
echo @echo off > "%TESTDIR%\test_aread8_sqlite.bat"
echo echo Testing TauDEM with SQLite >> "%TESTDIR%\test_aread8_sqlite.bat"
echo echo --------------------- >> "%TESTDIR%\test_aread8_sqlite.bat"
echo. >> "%TESTDIR%\test_aread8_sqlite.bat"
echo set APP_DIR=C:\Program Files\TauDEM >> "%TESTDIR%\test_aread8_sqlite.bat"
echo set GDAL_DATA=%%APP_DIR%%\share\gdal >> "%TESTDIR%\test_aread8_sqlite.bat"
echo set PROJ_LIB=%%APP_DIR%%\share\proj >> "%TESTDIR%\test_aread8_sqlite.bat"
echo set PATH=%%APP_DIR%%\bin;%%APP_DIR%%\TauDEM5Exe;%%PATH%% >> "%TESTDIR%\test_aread8_sqlite.bat"
echo set GDAL_DRIVER_PATH=%%APP_DIR%%\bin;%%APP_DIR%%\lib\gdalplugins >> "%TESTDIR%\test_aread8_sqlite.bat"
echo set OGR_DRIVER_PATH=%%APP_DIR%%\bin;%%APP_DIR%%\lib\gdalplugins >> "%TESTDIR%\test_aread8_sqlite.bat"
echo set GDAL_REGISTRY= >> "%TESTDIR%\test_aread8_sqlite.bat"
echo set OGR_ENABLED_DRIVERS= >> "%TESTDIR%\test_aread8_sqlite.bat"
echo. >> "%TESTDIR%\test_aread8_sqlite.bat"
echo echo Testing your specific command: >> "%TESTDIR%\test_aread8_sqlite.bat"
echo cd /d %%~dp0 >> "%TESTDIR%\test_aread8_sqlite.bat"
echo mpiexec -n 1 aread8 -p Y:\AreaD8_data\loganp.tif -o %%TESTDIR%%\LoganSample.sqlite -lyrname LoganOutlet -ad8 %%TESTDIR%%\loganad8_3.tif >> "%TESTDIR%\test_aread8_sqlite.bat"
echo. >> "%TESTDIR%\test_aread8_sqlite.bat"
echo if exist %%TESTDIR%%\loganad8_3.tif ( >> "%TESTDIR%\test_aread8_sqlite.bat"
echo   echo SUCCESS! Command completed successfully. >> "%TESTDIR%\test_aread8_sqlite.bat"
echo ) else ( >> "%TESTDIR%\test_aread8_sqlite.bat"
echo   echo FAILED: Command did not complete successfully. >> "%TESTDIR%\test_aread8_sqlite.bat"
echo ) >> "%TESTDIR%\test_aread8_sqlite.bat"
echo. >> "%TESTDIR%\test_aread8_sqlite.bat"
echo pause >> "%TESTDIR%\test_aread8_sqlite.bat"

echo.
echo A test script has been created at:
echo %TESTDIR%\test_aread8_sqlite.bat
echo.
echo INSTRUCTIONS:
echo 1. After applying these fixes, reboot your system
echo 2. Run the test script to verify if TauDEM can now work with SQLite files
echo 3. If issues persist, check the log file at %TEMP%\gdal_debug.log
echo.
echo Also review these common issues:
echo - Ensure all SQLite-related DLLs were installed correctly
echo - Make sure PATH includes the directory with TauDEM executables
echo - Verify sqlite3.dll is available in the bin directory
echo.
pause
