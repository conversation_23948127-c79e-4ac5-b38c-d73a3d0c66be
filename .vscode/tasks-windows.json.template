{"version": "2.0.0", "tasks": [{"label": "Build TauDEM (Debug) - Windows", "type": "shell", "command": "${workspaceFolder}/build-windows.bat", "args": [], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$msCompile"], "options": {"cwd": "${workspaceFolder}"}, "detail": "Build TauDEM in Debug mode using build-windows.bat script"}, {"label": "Build TauDEM (Release) - Windows", "type": "shell", "command": "${workspaceFolder}/build-windows.bat", "args": ["release"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$msCompile"], "options": {"cwd": "${workspaceFolder}"}, "detail": "Build TauDEM in Release mode using build-windows.bat script"}, {"label": "Clean Build - Windows", "type": "shell", "command": "cmd", "args": ["/c", "if exist build\\debug rmdir /s /q build\\debug && if exist build\\release rmdir /s /q build\\release && if exist bin rmdir /s /q bin"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}"}, "detail": "Clean all build directories and binaries"}, {"label": "Create Build Directories - Windows", "type": "shell", "command": "cmd", "args": ["/c", "if not exist \"build\\debug\" mkdir \"build\\debug\" && if not exist \"build\\release\" mkdir \"build\\release\" && echo Build directories created successfully."], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}"}, "detail": "Create build/debug and build/release directories if they don't exist"}, {"label": "CMake Configure (Debug) - Windows", "type": "shell", "command": "cmake", "args": ["-S", "../..", "-B", ".", "-C", "../../config.cmake", "-DCMAKE_BUILD_TYPE=Debug", "-A", "x64", "-DCMAKE_TOOLCHAIN_FILE=C:/dev/vcpkg/scripts/buildsystems/vcpkg.cmake"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}/build/debug"}, "detail": "Configure CMake build system for Debug mode with vcpkg"}, {"label": "CMake Configure (Release) - Windows", "type": "shell", "command": "cmake", "args": ["-S", "../..", "-B", ".", "-C", "../../config.cmake", "-DCMAKE_BUILD_TYPE=Release", "-A", "x64", "-DCMAKE_TOOLCHAIN_FILE=C:/dev/vcpkg/scripts/buildsystems/vcpkg.cmake"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}/build/release"}, "detail": "Configure CMake build system for Release mode with vcpkg"}, {"label": "CMake Build (Debug) - Windows", "type": "shell", "command": "cmake", "args": ["--build", ".", "--config", "Debug", "--parallel"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$msCompile"], "options": {"cwd": "${workspaceFolder}/build/debug"}, "dependsOn": "CMake Configure (Debug) - Windows", "detail": "Build TauDEM using CMake in Debug mode"}, {"label": "CMake Build (Release) - Windows", "type": "shell", "command": "cmake", "args": ["--build", ".", "--config", "Release", "--parallel"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$msCompile"], "options": {"cwd": "${workspaceFolder}/build/release"}, "dependsOn": "CMake Configure (Release) - Windows", "detail": "Build TauDEM using CMake in Release mode"}, {"label": "Run Specific TauDEM Tool (Debug) - Windows", "type": "shell", "command": "${input:taudemTool}", "args": ["${input:taudemArgs}"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}", "env": {"PATH": "${workspaceFolder}/build/debug/src/Debug;${env:PATH}", "GDAL_DATA": "C:/dev/vcpkg/installed/x64-windows/share/gdal", "PROJ_LIB": "C:/dev/vcpkg/installed/x64-windows/share/proj"}}, "detail": "Run a specific TauDEM tool with custom arguments (Debug build)"}, {"label": "Run Specific TauDEM Tool (Release) - Windows", "type": "shell", "command": "${input:taudemTool}", "args": ["${input:taudemArgs}"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}", "env": {"PATH": "${workspaceFolder}/build/release/src/Release;${env:PATH}", "GDAL_DATA": "C:/dev/vcpkg/installed/x64-windows/share/gdal", "PROJ_LIB": "C:/dev/vcpkg/installed/x64-windows/share/proj"}}, "detail": "Run a specific TauDEM tool with custom arguments (Release build)"}, {"label": "Check Dependencies - Windows", "type": "shell", "command": "powershell", "args": ["-Command", "Write-Host 'Checking TauDEM dependencies on Windows...'; Write-Host '=== CMake ==='; cmake --version; Write-Host '=== Visual Studio ==='; if (Get-Command cl -ErrorAction SilentlyContinue) { Write-Host 'Visual Studio compiler found' } else { Write-Host 'Visual Studio compiler not found in PATH' }; Write-Host '=== vcpkg ==='; if (Get-Command vcpkg -ErrorAction SilentlyContinue) { vcpkg version } else { Write-Host 'vcpkg not found in PATH' }; Write-Host '=== GDAL ==='; if (Get-Command gdalinfo -ErrorAction SilentlyContinue) { gdalinfo --version } else { Write-Host 'GDAL not found in PATH' }; Write-Host 'Dependencies check complete.'"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}"}, "detail": "Check if all required dependencies are installed and accessible"}, {"label": "Install TauDEM (for testing during development) - Windows", "type": "shell", "command": "cmd", "args": ["/c", "if not exist \"${input:installPath}\" mkdir \"${input:installPath}\" && xcopy /Y /I \"build\\release\\src\\Release\\*.*\" \"${input:installPath}\" && echo TauDEM installed to ${input:installPath}"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}"}, "dependsOn": "CMake Build (Release) - Windows", "detail": "Install TauDEM binaries to specified location (for testing during development)"}, {"label": "Build pitremove (Debug) - Windows", "type": "shell", "command": "cmake", "args": ["--build", ".", "--target", "<PERSON><PERSON>ove", "--config", "Debug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$msCompile"], "options": {"cwd": "${workspaceFolder}/build/debug"}, "dependsOn": "CMake Configure (Debug) - Windows", "detail": "Build pitremove target in Debug mode"}, {"label": "Build d8flowdir (Debug) - Windows", "type": "shell", "command": "cmake", "args": ["--build", ".", "--target", "d8flowdir", "--config", "Debug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$msCompile"], "options": {"cwd": "${workspaceFolder}/build/debug"}, "dependsOn": "CMake Configure (Debug) - Windows", "detail": "Build only d8flowdir target in Debug mode"}, {"label": "Build dinfflowdir (Debug) - Windows", "type": "shell", "command": "cmake", "args": ["--build", ".", "--target", "dinfflowdir", "--config", "Debug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$msCompile"], "options": {"cwd": "${workspaceFolder}/build/debug"}, "dependsOn": "CMake Configure (Debug) - Windows", "detail": "Build only dinfflowdir target in Debug mode"}, {"label": "Build aread8 (Debug) - Windows", "type": "shell", "command": "cmake", "args": ["--build", ".", "--target", "aread8", "--config", "Debug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$msCompile"], "options": {"cwd": "${workspaceFolder}/build/debug"}, "dependsOn": "CMake Configure (Debug) - Windows", "detail": "Build only aread8 target in Debug mode"}, {"label": "Build threshold (Debug) - Windows", "type": "shell", "command": "cmake", "args": ["--build", ".", "--target", "threshold", "--config", "Debug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$msCompile"], "options": {"cwd": "${workspaceFolder}/build/debug"}, "dependsOn": "CMake Configure (Debug) - Windows", "detail": "Build only threshold target in Debug mode"}, {"label": "Build streamnet (Debug) - Windows", "type": "shell", "command": "cmake", "args": ["--build", ".", "--target", "streamnet", "--config", "Debug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$msCompile"], "options": {"cwd": "${workspaceFolder}/build/debug"}, "dependsOn": "CMake Configure (Debug) - Windows", "detail": "Build only streamnet target in Debug mode"}, {"label": "Build gagewatershed (Debug) - Windows", "type": "shell", "command": "cmake", "args": ["--build", ".", "--target", "gagewatershed", "--config", "Debug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$msCompile"], "options": {"cwd": "${workspaceFolder}/build/debug"}, "dependsOn": "CMake Configure (Debug) - Windows", "detail": "Build only gagewatershed target in Debug mode"}, {"label": "Build dropanalysis (Debug) - Windows", "type": "shell", "command": "cmake", "args": ["--build", ".", "--target", "dropanalysis", "--config", "Debug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$msCompile"], "options": {"cwd": "${workspaceFolder}/build/debug"}, "dependsOn": "CMake Configure (Debug) - Windows", "detail": "Build only dropanalysis target in Debug mode"}, {"label": "Build twi (Debug) - Windows", "type": "shell", "command": "cmake", "args": ["--build", ".", "--target", "twi", "--config", "Debug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$msCompile"], "options": {"cwd": "${workspaceFolder}/build/debug"}, "dependsOn": "CMake Configure (Debug) - Windows", "detail": "Build only twi target in Debug mode"}, {"label": "Build d8hdisttostrm (Debug) - Windows", "type": "shell", "command": "cmake", "args": ["--build", ".", "--target", "d8hdisttostrm", "--config", "Debug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$msCompile"], "options": {"cwd": "${workspaceFolder}/build/debug"}, "dependsOn": "CMake Configure (Debug) - Windows", "detail": "Build only d8hdisttostrm target in Debug mode"}, {"label": "Build areadinf (Debug) - Windows", "type": "shell", "command": "cmake", "args": ["--build", ".", "--target", "areadinf", "--config", "Debug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$msCompile"], "options": {"cwd": "${workspaceFolder}/build/debug"}, "dependsOn": "CMake Configure (Debug) - Windows", "detail": "Build only areadinf target in Debug mode"}, {"label": "Build Generic Target (Debug) - Windows", "type": "shell", "command": "cmake", "args": ["--build", ".", "--target", "${input:targetName}", "--config", "Debug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$msCompile"], "options": {"cwd": "${workspaceFolder}/build/debug"}, "dependsOn": "CMake Configure (Debug) - Windows", "detail": "Build a specific target in Debug mode (prompts for target name)"}, {"label": "Build Generic Target (Release) - Windows", "type": "shell", "command": "cmake", "args": ["--build", ".", "--target", "${input:targetName}", "--config", "Release"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$msCompile"], "options": {"cwd": "${workspaceFolder}/build/release"}, "dependsOn": "CMake Configure (Release) - Windows", "detail": "Build a specific target in Release mode (prompts for target name)"}, {"label": "Uninstall TauDEM (for testing during development) - Windows", "type": "shell", "command": "cmd", "args": ["/c", "if exist \"${input:installPath}\" (rmdir /s /q \"${input:installPath}\" && echo TauDEM uninstalled from ${input:installPath}) else (echo TauDEM installation directory not found at ${input:installPath})"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}"}, "detail": "Remove TauDEM binaries from specified installation location"}, {"label": "Open TauDEM Documentation", "type": "shell", "command": "cmd", "args": ["/c", "start http://hydrology.usu.edu/taudem/taudem5/documentation.html"], "group": "test", "presentation": {"echo": true, "reveal": "never", "focus": false, "panel": "shared"}, "detail": "Open TauDEM documentation in default browser"}], "inputs": [{"id": "taudemTool", "description": "TauDEM tool to run", "default": "<PERSON><PERSON>ove", "type": "promptString"}, {"id": "taudemArgs", "description": "Arguments for the TauDEM tool", "default": "-z input.tif -fel output.tif", "type": "promptString"}, {"id": "installPath", "description": "Installation path for TauDEM binaries", "default": "C:/Program Files/TauDEM", "type": "promptString"}, {"id": "targetName", "description": "Target name to build", "default": "<PERSON><PERSON>ove", "type": "promptString"}]}