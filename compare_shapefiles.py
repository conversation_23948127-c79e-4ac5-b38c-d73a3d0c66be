#!/usr/bin/env python3

import sys
import os
import argparse
import geopandas as gpd
import numpy as np
from shapely.geometry import mapping

def compare_shp_files(file1_path, file2_path, tolerance=1e-6):
    """
    Compare two shapefiles for equality within a tolerance.
    Compares geometries, attributes, and coordinate systems.
    """
    try:
        # Read shapefiles
        gdf1 = gpd.read_file(file1_path)
        gdf2 = gpd.read_file(file2_path)
        
        # Compare CRS
        if gdf1.crs != gdf2.crs:
            print("Error: Files have different coordinate systems")
            return False
        
        # Compare number of features
        if len(gdf1) != len(gdf2):
            print(f"Error: Different number of features (File1: {len(gdf1)}, File2: {len(gdf2)})")
            return False
        
        # Compare attribute columns
        if set(gdf1.columns) != set(gdf2.columns):
            print("Error: Different attribute columns")
            return False
        
        # Sort both GeoDataFrames by geometry to ensure consistent comparison
        gdf1 = gdf1.sort_values(by=[col for col in gdf1.columns if col != 'geometry'])
        gdf2 = gdf2.sort_values(by=[col for col in gdf2.columns if col != 'geometry'])
        
        # Compare geometries and attributes
        for idx in range(len(gdf1)):
            # Compare geometry
            geom1 = mapping(gdf1.iloc[idx].geometry)
            geom2 = mapping(gdf2.iloc[idx].geometry)
            
            # Compare coordinates with tolerance
            if not np.allclose(np.array(geom1['coordinates']), 
                             np.array(geom2['coordinates']), 
                             rtol=tolerance):
                print(f"Error: Feature {idx} has different geometry")
                return False
            
            # Compare attributes
            for col in gdf1.columns:
                if col == 'geometry':
                    continue
                    
                val1 = gdf1.iloc[idx][col]
                val2 = gdf2.iloc[idx][col]
                
                # Handle numeric comparisons with tolerance
                if isinstance(val1, (int, float)) and isinstance(val2, (int, float)):
                    if not np.isclose(val1, val2, rtol=tolerance):
                        print(f"Error: Feature {idx}, attribute '{col}' has different values")
                        return False
                # Direct comparison for non-numeric values
                elif val1 != val2:
                    print(f"Error: Feature {idx}, attribute '{col}' has different values")
                    return False
        
        return True
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

def compare_directories(dir1_path, dir2_path, tolerance=1e-6):
    """
    Compare all matching shapefile files between two directories.
    Returns True only if all matching files are identical.
    """
    # Check if directories exist
    if not os.path.isdir(dir1_path) or not os.path.isdir(dir2_path):
        print("Error: One or both directory paths are invalid")
        return False

    # Get list of shapefiles in first directory
    shp_files = [f for f in os.listdir(dir1_path) if f.lower().endswith('.shp')]
    
    if not shp_files:
        print(f"No shapefiles found in {dir1_path}")
        return False

    all_identical = True
    files_compared = 0
    mismatched_files = 0
    missing_files = 0

    for shp_file in shp_files:
        file1_path = os.path.join(dir1_path, shp_file)
        file2_path = os.path.join(dir2_path, shp_file)

        if not os.path.exists(file2_path):
            print(f"Warning: {shp_file} not found in second directory")
            missing_files += 1
            all_identical = False
            continue

        print(f"\nComparing {shp_file}...")
        result = compare_shp_files(file1_path, file2_path, tolerance)
        print(f"{shp_file}: {'Identical' if result else 'Different'}")
        
        files_compared += 1
        if not result:
            mismatched_files += 1
            all_identical = False

    print("\nSummary:")
    print(f"Total shapefiles found in first directory: {len(shp_files)}")
    print(f"Files compared: {files_compared}")
    if mismatched_files > 0:
        print(f"Files with differences: {mismatched_files}")
    if missing_files > 0:
        print(f"Files missing from second directory: {missing_files}")
    print(f"Result: {'All files are identical' if all_identical else f'{mismatched_files + missing_files} file(s) did not match'}")
    
    return all_identical

def main():
    parser = argparse.ArgumentParser(description='Compare shapefile files between two directories')
    parser.add_argument('dir1', help='Path to first directory')
    parser.add_argument('dir2', help='Path to second directory')
    parser.add_argument('--tolerance', type=float, default=1e-6,
                      help='Tolerance for floating point comparisons (default: 1e-6)')
    
    args = parser.parse_args()
    
    result = compare_directories(args.dir1, args.dir2, args.tolerance)
    sys.exit(0 if result else 1)

if __name__ == '__main__':
    main()