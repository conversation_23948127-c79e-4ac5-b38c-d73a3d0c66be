@echo off
echo Installing SpatiaLite and dependencies with vcpkg
echo ===============================================
echo.

set VCPKG_DIR=C:\dev\vcpkg

if not exist "%VCPKG_DIR%" (
  echo ERROR: vcpkg directory not found at %VCPKG_DIR%
  echo Please install vcpkg first or update the script with the correct path.
  pause
  exit /b 1
)

echo Checking for existing SpatiaLite installation...
if exist "%VCPKG_DIR%\installed\x64-windows\bin\spatialite.dll" (
  echo [FOUND] SpatiaLite appears to be already installed
  
  echo Do you want to verify and update the installation? (Y/N)
  set /p CHOICE=Your choice: 
  if /i "%CHOICE%" NEQ "Y" (
    echo Exiting without changes.
    pause
    exit /b 0
  )
  echo Will verify and update existing installation...
) else (
  echo [NOT FOUND] SpatiaLite not detected, proceeding with installation...
)

echo Installing SpatiaLite and required dependencies...
echo This may take some time, please be patient.
echo.

cd /d "%VCPKG_DIR%"

echo Step 1: Installing GDAL with SpatiaLite support...
vcpkg install gdal[spatialite,sqlite3] --triplet=x64-windows

echo Step 2: Installing SQLite tools...
vcpkg install sqlite3 --triplet=x64-windows
vcpkg install sqlite3[tool] --triplet=x64-windows

echo Step 3: Installing SpatiaLite tools...
vcpkg install libspatialite --triplet=x64-windows
vcpkg install readosm --triplet=x64-windows
vcpkg install spatialite-tools --triplet=x64-windows

REM Add additional step to ensure mod_spatialite.dll is present
echo Step 4: Ensuring SpatiaLite extension module is available...
if not exist "%VCPKG_DIR%\installed\x64-windows\bin\mod_spatialite.dll" (
  echo [NOTICE] Attempting to locate mod_spatialite.dll...
  
  if exist "%VCPKG_DIR%\installed\x64-windows\lib\mod_spatialite.dll" (
    echo [FOUND] mod_spatialite.dll found in lib directory, copying to bin directory
    copy "%VCPKG_DIR%\installed\x64-windows\lib\mod_spatialite.dll" "%VCPKG_DIR%\installed\x64-windows\bin\" /y
  ) else (
    echo [WARNING] mod_spatialite.dll not found, trying alternative installation...
    vcpkg install spatialite --triplet=x64-windows
  )
)

REM Existing verification code continues...
echo Step 5: Verifying installation...
dir "%VCPKG_DIR%\installed\x64-windows\bin\spatialite.dll" 2>nul
if %ERRORLEVEL% NEQ 0 (
  echo [WARNING] spatialite.dll not found in expected location
) else (
  echo [OK] spatialite.dll found
)

dir "%VCPKG_DIR%\installed\x64-windows\bin\mod_spatialite.dll" 2>nul
if %ERRORLEVEL% NEQ 0 (
  echo [WARNING] mod_spatialite.dll not found in bin directory
) else (
  echo [OK] mod_spatialite.dll found in bin directory
)

dir "%VCPKG_DIR%\installed\x64-windows\lib\mod_spatialite.dll" 2>nul
if %ERRORLEVEL% NEQ 0 (
  echo [WARNING] mod_spatialite.dll not found in lib directory
) else (
  echo [OK] mod_spatialite.dll found in lib directory
)

echo Step 6: Testing SpatiaLite functionality...
set TEST_DIR=%TEMP%\spatialite_test
if exist "%TEST_DIR%" rmdir /s /q "%TEST_DIR%"
mkdir "%TEST_DIR%"

echo Testing SQLite with SpatiaLite extension...
if exist "%VCPKG_DIR%\installed\x64-windows\tools\sqlite3.exe" (
  echo Creating test database...
  "%VCPKG_DIR%\installed\x64-windows\tools\sqlite3.exe" "%TEST_DIR%\test.sqlite" ".load \"%VCPKG_DIR%\installed\x64-windows\bin\mod_spatialite\"" ".exit"
  
  if %ERRORLEVEL% EQU 0 (
    echo [SUCCESS] SpatiaLite extension loads correctly
  ) else (
    echo [WARNING] SpatiaLite extension may not be loading correctly
  )
) else (
  echo [WARNING] sqlite3.exe not found, skipping functional test
)

echo.
echo Installation verified. If you see any warnings above, some components
echo may still be missing or misconfigured.
echo.
echo Next steps:
echo 1. Compile your TauDEM installer with Inno Setup
echo 2. Make sure your installer copies spatialite.dll from:
echo    %VCPKG_DIR%\installed\x64-windows\bin\
echo 3. Also include mod_spatialite.dll in your installer
echo 4. Use the installer to install TauDEM
echo 5. Run the test_spatialite.bat script to verify SpatiaLite functionality
echo.
pause
