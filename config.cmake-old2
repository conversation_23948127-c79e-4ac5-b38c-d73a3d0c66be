# Set MPI compilers and paths
set(MPI_C_COMPILER "/opt/homebrew/bin/mpicc" CACHE PATH "MPI C compiler")
set(MPI_CXX_COMPILER "/opt/homebrew/bin/mpic++" CACHE PATH "MPI C++ compiler")
set(MPI_C_LIBRARIES "/opt/homebrew/lib/libmpi.dylib" CACHE PATH "MPI libraries")
set(MPI_C_INCLUDE_PATH "/opt/homebrew/include" CACHE PATH "MPI include path")

# Set GDAL installation path
set(CMAKE_PREFIX_PATH "/opt/homebrew/opt/gdal" CACHE PATH "GDAL installation path")

# Set installation directory relative to the project root directory
if(NOT CMAKE_INSTALL_PREFIX)
    set(CMAKE_INSTALL_PREFIX "${CMAKE_SOURCE_DIR}/install" CACHE PATH "Installation directory")
endif()

# Set build type if not already defined
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE "Debug" CACHE STRING "Build type (Debug/Release)")
endif()

# Conditionally set compilers if not already defined
if(NOT CMAKE_C_COMPILER)
    set(CMAKE_C_COMPILER "/usr/bin/clang" CACHE PATH "C compiler")
endif()

if(NOT CMAKE_CXX_COMPILER)
    set(CMAKE_CXX_COMPILER "/usr/bin/clang++" CACHE PATH "C++ compiler")
endif()