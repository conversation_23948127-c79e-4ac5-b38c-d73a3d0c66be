@echo off
echo SQLite Test for TauDEM and GDAL/OGR
echo ===================================

REM Setup environment variables similar to testfew.bat
set TDIR=C:\Program Files\TauDEM\TauDEM5Exe\Release
set MDIR=C:\Program Files\Microsoft MPI\Bin
set APP_DIR=C:\Program Files\TauDEM

REM Set GDAL environment variables
set GDAL_DATA=%APP_DIR%\share\gdal
set PROJ_LIB=%APP_DIR%\share\proj
set PATH=%MDIR%;%TDIR%;%APP_DIR%\bin;%PATH%

REM Enable detailed logging
set CPL_DEBUG=ON
set OGR_ENABLED_DRIVERS=ESRI Shapefile,SQLite,GeoJSON
set GDAL_DRIVER_PATH=%APP_DIR%\bin
set OGR_DRIVER_PATH=%APP_DIR%\bin

echo.
echo Current directory: %CD%
echo.

REM Test GDAL/OGR installation and SQLite support
echo Testing GDAL/OGR installation...
gdalinfo --version
ogrinfo --formats | findstr SQLite

echo.
echo Testing SQLite driver directly...
echo 1. Trying to create a simple SQLite file
ogrinfo -so -al "SQLite:test_ogr.sqlite"
if %ERRORLEVEL% NEQ 0 (
  echo Failed to create/access SQLite file with OGR
) else (
  echo Successfully created/accessed SQLite file with OGR
)

echo.
echo 2. Testing TauDEM with SQLite...
cd AreaD8_data
echo Current working directory: %CD%

REM Clean up any previous test files
if exist "LoganTest.sqlite" del LoganTest.sqlite

REM Run the SQLite test with verbose output
echo Running TauDEM AreaD8 with SQLite output...
mpiexec -n 1 aread8 -p loganp.tif -o LoganTest.sqlite -lyrname LoganOutlets -ad8 loganad8_test.tif -v
echo Exit code: %ERRORLEVEL%

REM Check if the SQLite file was created
if exist "LoganTest.sqlite" (
  echo SQLite file was created successfully
  echo Checking content with ogrinfo:
  ogrinfo -so -al LoganTest.sqlite
) else (
  echo ERROR: SQLite file was not created
)

echo.
echo Testing with alternate SQLite connection syntax...
mpiexec -n 1 aread8 -p loganp.tif -o "SQLite:LoganTest2.sqlite" -lyrname LoganOutlets -ad8 loganad8_test2.tif
echo Exit code: %ERRORLEVEL%

echo.
echo SQLite test complete
