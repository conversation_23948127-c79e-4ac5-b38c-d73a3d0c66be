{"configurations": [{"name": "<PERSON>", "includePath": ["${workspaceFolder}/**", "/opt/homebrew/include", "/opt/homebrew/opt/open-mpi/include"], "defines": [], "compilerPath": "/opt/homebrew/opt/open-mpi/bin/mpicxx", "cStandard": "c11", "cppStandard": "c++17", "intelliSenseMode": "macos-clang-arm64", "browse": {"path": ["${workspaceFolder}", "/opt/homebrew/include", "/opt/homebrew/opt/open-mpi/include"], "limitSymbolsToIncludedHeaders": true, "databaseFilename": ""}}, {"name": "Win32", "includePath": ["${workspaceFolder}/**", "C:/Program Files (x86)/Microsoft SDKs/MPI/Include", "C:/dev/vcpkg/installed/x64-windows/include", "C:/dev/vcpkg/installed/x64-windows/include"], "defines": ["_DEBUG", "UNICODE", "_UNICODE"], "compilerPath": "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.*/bin/Hostx64/x64/cl.exe", "cStandard": "c11", "cppStandard": "c++17", "intelliSenseMode": "windows-msvc-x64", "browse": {"path": ["${workspaceFolder}", "C:/Program Files (x86)/Microsoft SDKs/MPI/Include", "C:/dev/vcpkg/installed/x64-windows/include"], "limitSymbolsToIncludedHeaders": true, "databaseFilename": ""}}], "version": 4}