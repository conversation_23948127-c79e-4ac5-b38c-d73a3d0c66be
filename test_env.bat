@echo off
echo GDAL/OGR Comprehensive Test
echo ==========================
echo.

REM Set environment variables
set PROJ_LIB=C:\dev\vcpkg\installed\x64-windows\share\proj
set GDAL_DATA=C:\dev\vcpkg\installed\x64-windows\share\gdal
set GDAL_DRIVER_PATH=C:\dev\vcpkg\installed\x64-windows\bin
set OGR_DRIVER_PATH=C:\dev\vcpkg\installed\x64-windows\bin
set OGR_ENABLED_DRIVERS=ESRI Shapefile
set GDAL_REGISTRY=SQLITE:sqlite3.dll
set CPL_DEBUG=ON
set PATH=%PATH%;C:\dev\vcpkg\installed\x64-windows\tools\gdal

echo GDAL Environment:
echo ----------------
echo PROJ_LIB=%PROJ_LIB%
echo GDAL_DATA=%GDAL_DATA%
echo GDAL_DRIVER_PATH=%GDAL_DRIVER_PATH%
echo OGR_DRIVER_PATH=%OGR_DRIVER_PATH%
echo.

echo GDAL Version:
echo ------------
gdalinfo --version
echo.

echo Available Drivers:
echo ----------------
gdalinfo --formats | findstr "Shapefile"
ogrinfo --formats | findstr "ESRI Shapefile"
echo.

echo Testing Shapefile Creation:
echo -------------------------
set TESTDIR=%TEMP%\gdal_test2
if exist "%TESTDIR%" rmdir /s /q "%TESTDIR%"
mkdir "%TESTDIR%"

echo Creating test CSV file...
echo ID,WKT,Name > "%TESTDIR%\points.csv"
echo 1,"POINT(100 200)",Point1 >> "%TESTDIR%\points.csv"
echo 2,"POINT(300 400)",Point2 >> "%TESTDIR%\points.csv"

echo Converting CSV to shapefile...
ogr2ogr -f "ESRI Shapefile" "%TESTDIR%\points.shp" "%TESTDIR%\points.csv" -oo GEOM_POSSIBLE_NAMES=WKT

echo Checking if shapefile was created...
if exist "%TESTDIR%\points.shp" (
  echo Main SHP file exists
  if exist "%TESTDIR%\points.dbf" (
    echo DBF file exists
    if exist "%TESTDIR%\points.shx" (
      echo SHX file exists
      echo SUCCESS: Shapefile created successfully!
      
      echo.
      echo Shapefile details:
      ogrinfo -al "%TESTDIR%\points.shp"
    ) else (
      echo FAILED: SHX file missing
    )
  ) else (
    echo FAILED: DBF file missing
  )
) else (
  echo FAILED: SHP file missing
)

echo.
echo Directory contents:
dir "%TESTDIR%"

echo.
pause