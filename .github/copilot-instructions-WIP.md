# TauDEM

Every time you choose to apply a rule(s), explicitly state the rule(s) in the output. You can abbreviate the rule description to a single word or phrase.

## Project Context
TauDEM (Terrain Analysis Using Digital Elevation Models) is a suite of Digital Elevation Model (DEM) tools for the extraction and analysis of hydrologic information from topography as represented by a DEM.


## Code Style and Structure
- Write concise, C++ 17 code with accurate examples
- Prefer modern C++ constructs over legacy code
    - Use auto wisely to avoid redundancy but maintain readability
    - Prefer range-based for loops for iterable containers
    - Use smart pointers (e.g., std::unique_ptr, std::shared_ptr) instead of raw pointers for memory management
    - Avoid using magic numbers; define constants with meaningful names
    - Use lambdas for short, inline functions to improve readability
    - Always initialize variables at the point of declaration to avoid uninitialized values
    - Use std::move and std::forward to optimize performance when transferring ownership or forwarding arguments
    - Avoid using C-style casts; prefer static_cast, dynamic_cast, or reinterpret_cast for clarity and safety
- Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError)
- Structure repository files as follows:
```
server/
├── src/
    ├── components/     # Shared React components
    ├── hooks/          # Custom React hooks
    ├── utils/          # Helper functions
    ├── types/          # TypeScript types
    └── lib/            # Shared libraries
extension/
├── src/
    ├── background/     # Service worker scripts
    ├── content/        # Content scripts
    ├── popup/          # Extension popup UI
    ├── options/        # Extension options page
    ├── components/     # Shared React components
    ├── hooks/          # Custom React hooks
    ├── utils/          # Helper functions
    ├── lib/            # Shared libraries
    ├── types/          # TypeScript types
    └── storage/        # Chrome storage utilities
shared/
├── src/
    ├── types/          # TypeScript types, only used for shared types between server and extension
    └── utils/          # Helper functions, only used for shared functions between server and extension
```

## Tech Stack
- C++ 17
- MPI
- GDAL

## Naming Conventions
- Use lowercase with dashes for directories (e.g., components/form-wizard)
- Favor named exports for components and utilities
- Use PascalCase for cpp files (e.g., SinampSI.cpp)
- Use lowercase for header files (e.g., sindex.h)

## TypeScript Usage
- Use TypeScript for all code; prefer interfaces over types
- Avoid enums; use const objects with 'as const' assertion
- Use functional components with TypeScript interfaces
- Define strict types for message passing between different parts of the extension
- Use absolute imports for all files @/...
- Avoid try/catch blocks unless there's good reason to translate or handle error in that abstraction
- Use explicit return types for all functions

## Chrome Extension Specific
- Use Manifest V3 standards
- Implement proper message passing between components:
  ```typescript
  interface MessagePayload {
    type: string;
    data: unknown;
  }
  ```
- Handle permissions properly in manifest.json
- Use chrome.storage.local for persistent data
- Implement proper error boundaries and fallbacks
- Use lib/storage for storage related logic
- For the async injected scripts in content/, 
  - they must not close over variables from the outer scope
  - they must not use imported functions from the outer scope
  - they must have wrapped error handling so the error message is returned to the caller

## State Management
- Use React Context for global state when needed
- Implement proper state persistence using chrome.storage (for extension)
- Implement proper cleanup in useEffect hooks

## Syntax and Formatting
- Use "function" keyword for pure functions
- Avoid unnecessary curly braces in conditionals
- Use declarative JSX
- Implement proper TypeScript discriminated unions for message types

## UI and Styling
- Use Shadcn UI and Radix for components
- use `npx shadcn@latest add <component-name>` to add new shadcn components
- Implement Tailwind CSS for styling
- Consider extension-specific constraints (popup dimensions, permissions)
- Follow Material Design guidelines for Chrome extensions
- When adding new shadcn component, document the installation command

## Error Handling
- Implement proper error boundaries
- Log errors appropriately for debugging
- Provide user-friendly error messages

## Testing
- Write unit tests for utilities and components
- Implement E2E tests for critical flows
- Test across different Chrome versions
- Test memory usage and performance

## Security
- Implement Content Security Policy
- Sanitize user inputs
- Handle sensitive data properly
- Follow Chrome extension security best practices
- Implement proper CORS handling

## Git Usage
Commit Message Prefixes:
- "fix:" for bug fixes
- "feat:" for new features
- "perf:" for performance improvements
- "docs:" for documentation changes
- "style:" for formatting changes
- "refactor:" for code refactoring
- "test:" for adding missing tests
- "chore:" for maintenance tasks

Rules:
- Use lowercase for commit messages
- Keep the summary line concise
- Include description for non-obvious changes
- Reference issue numbers when applicable

## Documentation
- Maintain clear README with setup instructions
- Document API interactions and data flows
- Keep manifest.json well-documented
- Don't include comments unless it's for complex logic
- Document permission requirements

## Development Workflow
- Use proper version control
- Implement proper code review process
- Test in multiple environments
- Follow semantic versioning for releases
- Maintain changelog