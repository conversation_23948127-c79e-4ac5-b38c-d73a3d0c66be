@echo off
echo OGR Data Source Error Troubleshooting
echo ===================================
echo.

echo This script will help diagnose and fix OGR data source errors in TauDEM.
echo.

REM Check if vcpkg is installed
set VCPKG_DIR=C:\dev\vcpkg
if not exist "%VCPKG_DIR%" (
  echo [ERROR] vcpkg not found at %VCPKG_DIR%
  echo You need to install vcpkg first.
  echo Visit: https://github.com/microsoft/vcpkg
  pause
  exit /b 1
)

echo Step 1: Checking for SpatiaLite installation in vcpkg...
if exist "%VCPKG_DIR%\installed\x64-windows\bin\spatialite.dll" (
  echo [FOUND] SpatiaLite appears to be installed
) else (
  echo [MISSING] SpatiaLite not installed in vcpkg
  echo.
  echo To install SpatiaLite and fix OGR errors, run:
  echo y:\install_spatialite_vcpkg.bat
  echo.
  echo After installation completes, rebuild your TauDEM installer.
  pause
  exit /b 1
)

echo Step 2: Checking if TauDEM is installed...
set TAUDEM_INSTALLED=0
for %%D in (C D E F G H I J K L M N O P Q R S T U V W X Y Z) do (
  if exist "%%D:\Program Files\TauDEM\TauDEM5Exe" (
    set TAUDEM_DIR=%%D:\Program Files\TauDEM
    set TAUDEM_INSTALLED=1
  )
)

if "%TAUDEM_INSTALLED%"=="0" (
  echo [WARNING] TauDEM installation not found
  echo This script works best with TauDEM already installed.
  echo.
  echo Please install TauDEM first, then run this script again.
  pause
  exit /b 1
)

echo [FOUND] TauDEM installation at %TAUDEM_DIR%

echo Step 3: Checking for SpatiaLite DLLs in TauDEM installation...
if exist "%TAUDEM_DIR%\bin\spatialite.dll" (
  echo [FOUND] spatialite.dll in TauDEM installation
) else (
  echo [MISSING] spatialite.dll not in TauDEM installation
  echo This could be causing your OGR data source error.
)

if exist "%TAUDEM_DIR%\bin\mod_spatialite.dll" (
  echo [FOUND] mod_spatialite.dll in TauDEM installation
) else (
  echo [MISSING] mod_spatialite.dll not in TauDEM installation
  echo This could be causing your OGR data source error.
)

echo.
echo Step 4: Checking environment variables...
echo.

echo Current OGR_DRIVER_PATH: %OGR_DRIVER_PATH%
echo Current GDAL_DRIVER_PATH: %GDAL_DRIVER_PATH%
echo Current SPATIALITE_SECURITY: %SPATIALITE_SECURITY%

echo.
echo Step 5: Testing basic OGR functionality...
set TESTDIR=%TEMP%\ogr_test
if exist "%TESTDIR%" rmdir /s /q "%TESTDIR%"
mkdir "%TESTDIR%"

echo Creating a test point...
echo ID,X,Y > "%TESTDIR%\point.csv"
echo 1,100,200 >> "%TESTDIR%\point.csv"

echo Testing shapefile creation...
"%TAUDEM_DIR%\bin\ogr2ogr.exe" -f "ESRI Shapefile" "%TESTDIR%\point.shp" "%TESTDIR%\point.csv" -oo X_POSSIBLE_NAMES=X -oo Y_POSSIBLE_NAMES=Y -oo GEOMETRY=AS_XY
if %ERRORLEVEL% EQU 0 (
  echo [SUCCESS] Basic OGR functionality is working
) else (
  echo [FAILED] Basic OGR functionality is not working
  echo This confirms you have an OGR data source error.
)

echo.
echo Step 6: Summary and Recommendations
echo -----------------------------------
echo.
echo Based on the tests above:
echo.
echo 1. Install SpatiaLite using the script: y:\install_spatialite_vcpkg.bat
echo 2. Rebuild your TauDEM installer
echo 3. After installing TauDEM, run the test_spatialite.bat script
echo.

echo Environment variables to set manually if problems persist:
echo setx GDAL_DRIVER_PATH "%TAUDEM_DIR%\bin;%TAUDEM_DIR%\lib\gdalplugins" /M
echo setx OGR_DRIVER_PATH "%TAUDEM_DIR%\bin;%TAUDEM_DIR%\lib\gdalplugins" /M
echo setx SPATIALITE_SECURITY "relaxed" /M
echo setx SPATIALITE_LIBRARY_PATH "%TAUDEM_DIR%\bin\mod_spatialite.dll" /M
echo.

echo To make these changes, press any key to run these commands with admin privileges...
pause > nul

echo @echo off > "%TEMP%\fix_ogr_env.bat"
echo echo Setting environment variables for OGR... >> "%TEMP%\fix_ogr_env.bat"
echo setx GDAL_DRIVER_PATH "%TAUDEM_DIR%\bin;%TAUDEM_DIR%\lib\gdalplugins" /M >> "%TEMP%\fix_ogr_env.bat"
echo setx OGR_DRIVER_PATH "%TAUDEM_DIR%\bin;%TAUDEM_DIR%\lib\gdalplugins" /M >> "%TEMP%\fix_ogr_env.bat"
echo setx SPATIALITE_SECURITY "relaxed" /M >> "%TEMP%\fix_ogr_env.bat"
echo if exist "%TAUDEM_DIR%\bin\mod_spatialite.dll" ( >> "%TEMP%\fix_ogr_env.bat"
echo   setx SPATIALITE_LIBRARY_PATH "%TAUDEM_DIR%\bin\mod_spatialite.dll" /M >> "%TEMP%\fix_ogr_env.bat"
echo ) >> "%TEMP%\fix_ogr_env.bat"
echo echo Environment variables set. Please reboot your system. >> "%TEMP%\fix_ogr_env.bat"
echo pause >> "%TEMP%\fix_ogr_env.bat"

powershell -Command "Start-Process cmd -ArgumentList '/c %TEMP%\fix_ogr_env.bat' -Verb RunAs"

echo.
echo Script completed. Follow the recommendations above to fix your OGR data source error.
pause
