@echo off
setlocal enabledelayedexpansion

echo TauDEM Installation Script for Windows
echo =====================================
echo.

REM Set installation directory (adjust as needed)
set INSTALL_DIR=C:\Program Files\TauDEM

REM Check for administrator privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Warning: This script is not running with administrator privileges.
    echo Some operations may fail. Consider running as administrator.
    echo.
    pause
)

REM Check for vcpkg
echo Checking for vcpkg...
set VCPKG_FOUND=0
set VCPKG_CMD=

REM Check common vcpkg locations
if exist "C:\dev\vcpkg\vcpkg.exe" (
    set VCPKG_FOUND=1
    set VCPKG_CMD=C:\dev\vcpkg\vcpkg.exe
    echo Found vcpkg at C:\dev\vcpkg
) else if exist "C:\vcpkg\vcpkg.exe" (
    set VCPKG_FOUND=1
    set VCPKG_CMD=C:\vcpkg\vcpkg.exe
    echo Found vcpkg at C:\vcpkg
) else (
    echo vcpkg not found in common locations.
    echo.
    echo vcpkg is recommended for installing dependencies (GDAL and MPI).
    echo To install vcpkg:
    echo 1. Open a command prompt with administrator privileges
    echo 2. Run these commands:
    echo    git clone https://github.com/Microsoft/vcpkg.git C:\dev\vcpkg
    echo    cd C:\dev\vcpkg
    echo    .\bootstrap-vcpkg.bat
    echo    .\vcpkg integrate install
    echo.
    echo 3. After installing vcpkg, you can install required packages with:
    echo    .\vcpkg install gdal:x64-windows mpi:x64-windows
    echo.
    set /p VCPKG_INSTALL=Would you like to install VCPKG now? (y/n): 
    if /i "!VCPKG_INSTALL!"=="y" (
        echo Installing vcpkg...
        
        REM Check if git is available
        where git >nul 2>&1
        if %errorLevel% neq 0 (
            echo Error: Git is required but not found. Please install Git first.
            echo You can download Git from: https://git-scm.com/downloads
            goto check_dependencies
        )
        
        REM Clone and bootstrap vcpkg
        echo Creating C:\dev directory...
        mkdir C:\dev 2>nul
        
        echo Cloning vcpkg repository...
        cd C:\dev
        git clone https://github.com/Microsoft/vcpkg.git
        
        if %errorLevel% neq 0 (
            echo Error: Failed to clone vcpkg repository.
            cd /d %~dp0
            goto check_dependencies
        )
        
        echo Bootstrap vcpkg...
        cd vcpkg
        call bootstrap-vcpkg.bat
        
        if %errorLevel% neq 0 (
            echo Error: Failed to bootstrap vcpkg.
            cd /d %~dp0
            goto check_dependencies
        )
        
        echo Integrating vcpkg with Visual Studio...
        .\vcpkg integrate install
        
        echo vcpkg installation complete!
        set VCPKG_FOUND=1
        set VCPKG_CMD=C:\dev\vcpkg\vcpkg.exe
        
        cd /d %~dp0
    )
)

:check_dependencies
echo.
echo Checking dependencies...

REM Check for GDAL
set GDAL_FOUND=0
where gdalinfo >nul 2>&1
if %errorLevel% equ 0 (
    set GDAL_FOUND=1
    for /f "tokens=*" %%a in ('gdalinfo --version') do set GDAL_VERSION=%%a
    echo Found GDAL: !GDAL_VERSION!
) else (
    echo GDAL not found in PATH
    
    REM Check in common installation locations
    if exist "C:\Program Files\GDAL\gdalinfo.exe" (
        set GDAL_FOUND=1
        echo Found GDAL in C:\Program Files\GDAL
    ) else if exist "C:\OSGeo4W64\bin\gdalinfo.exe" (
        set GDAL_FOUND=1
        echo Found GDAL in C:\OSGeo4W64\bin
    ) else if exist "C:\dev\vcpkg\installed\x64-windows\tools\gdal\gdalinfo.exe" (
        set GDAL_FOUND=1
        echo Found GDAL in vcpkg installation
    )
)

if !GDAL_FOUND! equ 0 (
    echo.
    echo GDAL is required but not found. You can install it using one of these methods:
    
    if !VCPKG_FOUND! equ 1 (
        echo 1. Use vcpkg (Recommended): !VCPKG_CMD! install gdal:x64-windows
        set /p INSTALL_GDAL=Would you like to install GDAL via vcpkg now? (y/n): 
        if /i "!INSTALL_GDAL!"=="y" (
            echo Installing GDAL via vcpkg...
            "!VCPKG_CMD!" install gdal:x64-windows
            if %errorLevel% equ 0 (
                set GDAL_FOUND=1
                echo GDAL installation complete!
            ) else (
                echo Error: Failed to install GDAL.
            )
        )
    ) else (
        echo 1. Install vcpkg first, then use: vcpkg install gdal:x64-windows
    )
    
    echo 2. Download and install OSGeo4W: https://trac.osgeo.org/osgeo4w/
    echo 3. Download standalone GDAL binaries from GIS Internals: https://www.gisinternals.com/release.php
    echo.
    
    if !GDAL_FOUND! equ 0 (
        set /p CONTINUE=Do you want to continue with installation anyway? (y/n): 
        if /i "!CONTINUE!" neq "y" (
            echo Installation aborted.
            goto end
        )
    )
)

REM Check for MPI
set MPI_FOUND=0
where mpiexec >nul 2>&1
if %errorLevel% equ 0 (
    set MPI_FOUND=1
    for /f "tokens=*" %%a in ('mpiexec -version 2^>^&1') do set MPI_VERSION=%%a
    echo Found MPI: !MPI_VERSION!
) else (
    echo MPI executables not found in PATH
    
    REM Check in common installation locations
    if exist "C:\Program Files\Microsoft MPI\Bin\mpiexec.exe" (
        set MPI_FOUND=1
        echo Found Microsoft MPI in C:\Program Files\Microsoft MPI
    ) else if exist "C:\dev\vcpkg\installed\x64-windows\tools\mpi\mpiexec.exe" (
        set MPI_FOUND=1
        echo Found MPI in vcpkg installation
    )
)

if !MPI_FOUND! equ 0 (
    echo.
    echo MPI is required but not found. You can install it using one of these methods:
    
    if !VCPKG_FOUND! equ 1 (
        echo 1. Use vcpkg (Recommended): !VCPKG_CMD! install mpi:x64-windows
        set /p INSTALL_MPI=Would you like to install MPI via vcpkg now? (y/n): 
        if /i "!INSTALL_MPI!"=="y" (
            echo Installing MPI via vcpkg...
            "!VCPKG_CMD!" install mpi:x64-windows
            if %errorLevel% equ 0 (
                set MPI_FOUND=1
                echo MPI installation complete!
            ) else (
                echo Error: Failed to install MPI.
            )
        )
    ) else (
        echo 1. Install vcpkg first, then use: vcpkg install mpi:x64-windows
    )
    
    echo 2. Download and install Microsoft MPI from: https://www.microsoft.com/en-us/download/details.aspx?id=57467
    echo.
    
    if !MPI_FOUND! equ 0 (
        set /p CONTINUE=Do you want to continue with installation anyway? (y/n): 
        if /i "!CONTINUE!" neq "y" (
            echo Installation aborted.
            goto end
        )
    )
)

echo.
echo Dependency check complete.
echo.

REM Set build directory based on configuration
set BUILD_DIR=src\build
set CONFIG_DIR=Debug
if "%1"=="release" (
    set BUILD_DIR=src\build-release
    set CONFIG_DIR=Release
)

REM Check if build directory exists
if not exist %BUILD_DIR%\%CONFIG_DIR% (
    echo Error: Build directory %BUILD_DIR%\%CONFIG_DIR% not found.
    echo Please build the project first with:
    if "%1"=="release" (
        echo   build-windows.bat release
    ) else (
        echo   build-windows.bat
    )
    goto end
)

REM Create installation directory if it doesn't exist
echo Creating installation directory...
if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%" 2>nul
    if %errorLevel% neq 0 (
        echo Error: Failed to create installation directory. Try running as administrator.
        goto end
    )
)

echo Copying executables to "%INSTALL_DIR%"...

REM Copy all executable files to the installation directory
for %%f in (%BUILD_DIR%\%CONFIG_DIR%\*.exe) do (
    echo Installing %%~nxf...
    copy "%%f" "%INSTALL_DIR%\" >nul 2>&1
    if %errorLevel% neq 0 (
        echo Error: Failed to copy "%%~nxf". Try running as administrator.
        goto end
    )
)

REM Copy runtime dependencies if they exist
if exist %BUILD_DIR%\%CONFIG_DIR%\*.dll (
    echo Copying runtime dependencies...
    for %%f in (%BUILD_DIR%\%CONFIG_DIR%\*.dll) do (
        echo Installing %%~nxf...
        copy "%%f" "%INSTALL_DIR%\" >nul 2>&1
    )
)

echo.
echo Would you like to add the installation directory to the system PATH?
set /p ADD_TO_PATH=This requires administrator privileges (y/n): 

if /i "%ADD_TO_PATH%"=="y" (
    echo Adding "%INSTALL_DIR%" to system PATH...
    setx PATH "%PATH%;%INSTALL_DIR%" /M >nul 2>&1
    if %errorLevel% neq 0 (
        echo Failed to modify PATH. Try running as administrator.
    ) else (
        echo Successfully added to PATH.
        echo NOTE: You need to restart your command prompt for PATH changes to take effect.
    )
)

echo.
echo Installation complete!
echo TauDEM executables have been installed to "%INSTALL_DIR%"
echo.

REM Create a simple test batch file
echo @echo off > "%INSTALL_DIR%\taudem-test.bat"
echo echo Testing TauDEM installation... >> "%INSTALL_DIR%\taudem-test.bat"
echo echo. >> "%INSTALL_DIR%\taudem-test.bat"
echo aread8 -h >> "%INSTALL_DIR%\taudem-test.bat"
echo echo. >> "%INSTALL_DIR%\taudem-test.bat"
echo echo If you see the help message above, TauDEM is installed correctly. >> "%INSTALL_DIR%\taudem-test.bat"
echo pause >> "%INSTALL_DIR%\taudem-test.bat"

echo A test script has been created at "%INSTALL_DIR%\taudem-test.bat"
echo Run this script to verify your installation.

:end
endlocal
pause